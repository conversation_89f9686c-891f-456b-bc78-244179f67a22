//
//  MetalRenderer.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/12.
//

import Metal
import MetalKit
import CoreImage
import AVFoundation
import MetalPerformanceShaders

class MetalRenderer {
    // Metal objects
    let device: MTLDevice
    private let commandQueue: MTLCommandQueue
    private var pipelineState: MTLRenderPipelineState?
    private var thresholdPipelineState: MTLRenderPipelineState?
    private var bloomPipelineState: MTLRenderPipelineState?

    // Texture cache for converting CMSampleBuffer to Metal texture
    private var textureCache: CVMetalTextureCache?
    private let textureCacheQueue = DispatchQueue(label: "com.latenightking.TrueFilm.textureCache", qos: .userInteractive)

    // Render targets
    private var originalTexture: MTLTexture?
    private var brightTexture: MTLTexture?
    private var blurredTexture: MTLTexture?
    private var intermediateBlurTexture: MTLTexture?  // 用于多级模糊的中间纹理
    private var outputTexture: MTLTexture?

    // Vertex buffer for rendering a quad
    private var quadVertexBuffer: MTLBuffer?

    // Filter parameters
    private var thresholdValue: Float = 0.03  // 默认阈值设置为0.50
    private var bloomIntensity: Float = 0.3  // 默认强度为0.3
    private var brightnessValue: Float = 0  // 默认亮度为0

    // Highlight tint values (RGB)
    private var redTintValue: Float = 0.0  // 默认高光红色偏色值
    private var greenTintValue: Float = 0.0  // 默认高光绿色偏色值
    private var blueTintValue: Float = 0.0  // 默认高光蓝色偏色值

    // Filter state
    private(set) var isBlackMistEnabled = false

    // Frame counter for debugging
    private var processFrameCount = 0

    // Initialize Metal renderer
    init?() {
        // Get default Metal device
        guard let device = MTLCreateSystemDefaultDevice() else {
            print("Metal is not supported on this device")
            return nil
        }
        self.device = device

        // Create command queue
        guard let commandQueue = device.makeCommandQueue() else {
            print("Could not create Metal command queue")
            return nil
        }
        self.commandQueue = commandQueue

        // Create texture cache
        var textureCache: CVMetalTextureCache?
        CVMetalTextureCacheCreate(nil, nil, device, nil, &textureCache)
        self.textureCache = textureCache

        // Create quad vertex buffer
        createQuadVertexBuffer()

        // Set up render pipeline
        setupRenderPipelines()
    }

    // Create a vertex buffer for a quad that fills the screen
    private func createQuadVertexBuffer() {
        // Define quad vertices (two triangles)
        let quadVertices: [Float] = [
            -1.0, -1.0, 0.0, 1.0,  // bottom left
             1.0, -1.0, 1.0, 1.0,  // bottom right
            -1.0,  1.0, 0.0, 0.0,  // top left
             1.0,  1.0, 1.0, 0.0,  // top right
        ]

        // Create vertex buffer
        quadVertexBuffer = device.makeBuffer(bytes: quadVertices,
                                            length: quadVertices.count * MemoryLayout<Float>.size,
                                            options: .storageModeShared)
    }

    // Set up the render pipeline states
    private func setupRenderPipelines() {
        // Load default library
        guard let library = device.makeDefaultLibrary() else {
            print("Could not load default Metal library")
            return
        }

        // Set up basic render pipeline
        setupBasicRenderPipeline(library: library)

        // Set up threshold pipeline
        setupThresholdPipeline(library: library)

        // Set up bloom pipeline
        setupBloomPipeline(library: library)
    }

    // Set up the basic render pipeline state
    private func setupBasicRenderPipeline(library: MTLLibrary) {
        // Get vertex and fragment functions
        guard let vertexFunction = library.makeFunction(name: "vertexShader"),
              let fragmentFunction = library.makeFunction(name: "fragmentShader") else {
            print("Could not find basic Metal shader functions")
            return
        }

        // Create render pipeline descriptor
        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.colorAttachments[0].pixelFormat = .bgra8Unorm

        // Create pipeline state
        do {
            pipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        } catch {
            print("Failed to create basic render pipeline state: \(error)")
        }
    }

    // Set up the threshold pipeline state
    private func setupThresholdPipeline(library: MTLLibrary) {
        // Get vertex and fragment functions
        guard let vertexFunction = library.makeFunction(name: "vertexShader"),
              let fragmentFunction = library.makeFunction(name: "thresholdShader") else {
            print("Could not find threshold Metal shader functions")
            return
        }

        // Create render pipeline descriptor
        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.colorAttachments[0].pixelFormat = .bgra8Unorm

        // Create pipeline state
        do {
            thresholdPipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        } catch {
            print("Failed to create threshold pipeline state: \(error)")
        }
    }

    // Set up the bloom pipeline state
    private func setupBloomPipeline(library: MTLLibrary) {
        // Get vertex and fragment functions
        guard let vertexFunction = library.makeFunction(name: "vertexShader"),
              let fragmentFunction = library.makeFunction(name: "bloomShader") else {
            print("Could not find bloom Metal shader functions")
            return
        }

        // Create render pipeline descriptor
        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.colorAttachments[0].pixelFormat = .bgra8Unorm

        // Create pipeline state
        do {
            bloomPipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        } catch {
            print("Failed to create bloom pipeline state: \(error)")
        }
    }

    // Create a texture with the same dimensions as the input texture
    private func createTexture(matchingTexture texture: MTLTexture) -> MTLTexture? {
        let descriptor = MTLTextureDescriptor.texture2DDescriptor(
            pixelFormat: texture.pixelFormat,
            width: texture.width,
            height: texture.height,
            mipmapped: false)

        descriptor.usage = [.renderTarget, .shaderRead, .shaderWrite]
        descriptor.storageMode = .private

        return device.makeTexture(descriptor: descriptor)
    }

    // Convert CMSampleBuffer to Metal texture
    func texture(from sampleBuffer: CMSampleBuffer) -> MTLTexture? {
        return textureCacheQueue.sync {
            guard let textureCache = textureCache,
                  let imageBuffer = CMSampleBufferGetImageBuffer(sampleBuffer) else {
                return nil
            }

            let width = CVPixelBufferGetWidth(imageBuffer)
            let height = CVPixelBufferGetHeight(imageBuffer)

            // Create Metal texture from CVPixelBuffer
            var texture: CVMetalTexture?
            let status = CVMetalTextureCacheCreateTextureFromImage(
                nil, textureCache, imageBuffer, nil, .bgra8Unorm,
                width, height, 0, &texture
            )

            guard status == kCVReturnSuccess, let metalTexture = texture, let outputTexture = CVMetalTextureGetTexture(metalTexture) else {
                return nil
            }

            return outputTexture
        }
    }

    // Toggle Black Mist effect
    func toggleBlackMist() {
        isBlackMistEnabled = !isBlackMistEnabled
        print("MetalRenderer: Black Mist \(isBlackMistEnabled ? "enabled" : "disabled"), thresholdValue=\(thresholdValue), bloomIntensity=\(bloomIntensity), brightnessValue=\(brightnessValue)")

        // Force recreate textures when toggling to ensure proper initialization
        if isBlackMistEnabled {
            // Release existing textures to force recreation
            brightTexture = nil
            blurredTexture = nil
            intermediateBlurTexture = nil
            outputTexture = nil
        }
    }

    // Set Black Mist threshold value
    func setThreshold(_ value: Float) {
        thresholdValue = max(0.0, min(1.0, value))
    }

    // Set Black Mist intensity
    func setBloomIntensity(_ value: Float) {
        bloomIntensity = max(0.0, min(2.0, value))
    }

    // Set brightness value
    func setBrightness(_ value: Float) {
        brightnessValue = max(-1.0, min(1.0, value))
    }

    // Set red tint value
    func setRedTint(_ value: Float) {
        redTintValue = max(0.0, min(2.0, value))
    }

    // Set green tint value
    func setGreenTint(_ value: Float) {
        greenTintValue = max(0.0, min(2.0, value))
    }

    // Set blue tint value
    func setBlueTint(_ value: Float) {
        blueTintValue = max(0.0, min(2.0, value))
    }

    // Clean up textures to prevent memory leaks
    func cleanupTextures() {
        brightTexture = nil
        blurredTexture = nil
        intermediateBlurTexture = nil
        outputTexture = nil
        originalTexture = nil

        // Flush texture cache to release memory
        if let textureCache = textureCache {
            CVMetalTextureCacheFlush(textureCache, 0)
        }
    }

    // Process a frame with Metal
    func processFrame(texture: MTLTexture) -> MTLTexture? {
        return autoreleasepool {
            // Always store the original texture
            originalTexture = texture

        // If brightness adjustment is needed but Black Mist is not enabled,
        // apply only brightness adjustment
        if !isBlackMistEnabled && brightnessValue != 0.0 {
            // Create output texture if needed
            if outputTexture == nil || outputTexture?.width != texture.width || outputTexture?.height != texture.height {
                outputTexture = createTexture(matchingTexture: texture)
            }

            guard let outputTexture = outputTexture,
                  let commandBuffer = commandQueue.makeCommandBuffer(),
                  let pipelineState = pipelineState,
                  let quadVertexBuffer = quadVertexBuffer else {
                return texture
            }

            // Create render pass descriptor
            let renderPassDescriptor = MTLRenderPassDescriptor()
            renderPassDescriptor.colorAttachments[0].texture = outputTexture
            renderPassDescriptor.colorAttachments[0].loadAction = .clear
            renderPassDescriptor.colorAttachments[0].clearColor = MTLClearColor(red: 0, green: 0, blue: 0, alpha: 1)
            renderPassDescriptor.colorAttachments[0].storeAction = .store

            // Create render command encoder
            guard let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor) else {
                return texture
            }

            // Set render pipeline state
            renderEncoder.setRenderPipelineState(pipelineState)

            // Set vertex buffer
            renderEncoder.setVertexBuffer(quadVertexBuffer, offset: 0, index: 0)

            // Set fragment texture
            renderEncoder.setFragmentTexture(texture, index: 0)

            // Set brightness value
            renderEncoder.setFragmentBytes(&brightnessValue, length: MemoryLayout<Float>.size, index: 0)

            // Draw quad
            renderEncoder.drawPrimitives(type: .triangleStrip, vertexStart: 0, vertexCount: 4)

            // End encoding
            renderEncoder.endEncoding()

            // Commit command buffer
            commandBuffer.commit()

            return outputTexture
        }

        // 减少日志输出频率，避免性能问题
        processFrameCount += 1

        // If Black Mist is not enabled and no brightness adjustment, return the original texture
        if !isBlackMistEnabled {
            // 减少日志频率 - 每 600 帧打印一次（约10秒一次）
            if processFrameCount % 600 == 0 {
                print("MetalRenderer.processFrame: Black Mist not enabled, returning original texture")
            }
            return texture
        }

        // 减少调试信息输出频率 - 每 600 帧打印一次（约10秒一次）
        if processFrameCount % 600 == 0 {
            print("MetalRenderer.processFrame: Processing frame with Black Mist, thresholdValue=\(thresholdValue), bloomIntensity=\(bloomIntensity), brightnessValue=\(brightnessValue)")
        }

        // Process the frame with Black Mist filter

        // Create textures for processing if needed
        if brightTexture == nil || brightTexture?.width != texture.width || brightTexture?.height != texture.height {
            // Clean up old textures first to prevent memory leaks
            cleanupTextures()

            brightTexture = createTexture(matchingTexture: texture)
            blurredTexture = createTexture(matchingTexture: texture)
            intermediateBlurTexture = createTexture(matchingTexture: texture)
            outputTexture = createTexture(matchingTexture: texture)
        }

        guard let brightTexture = brightTexture,
              let blurredTexture = blurredTexture,
              let intermediateBlurTexture = intermediateBlurTexture,
              let outputTexture = outputTexture,
              let quadVertexBuffer = quadVertexBuffer else {
            return texture
        }

        // Create command buffer
        guard let commandBuffer = commandQueue.makeCommandBuffer() else {
            return texture
        }

        // Step 1: Extract bright areas using threshold shader
        extractBrightAreas(commandBuffer: commandBuffer, inputTexture: texture, outputTexture: brightTexture)

        // Step 2: Blur the bright areas with multi-level blur for better diffusion
        blurTextureMultiLevel(commandBuffer: commandBuffer, inputTexture: brightTexture, intermediateTexture: intermediateBlurTexture, outputTexture: blurredTexture)

        // Step 3: Combine original and blurred textures
        combineTextures(commandBuffer: commandBuffer, originalTexture: texture, blurredTexture: blurredTexture, outputTexture: outputTexture)

        // Commit command buffer
        commandBuffer.commit()

        // Don't wait for completion to avoid blocking the main thread
        // This allows for smoother real-time preview
        // commandBuffer.waitUntilCompleted()

        return outputTexture
        }
    }

    // Extract bright areas from the input texture
    private func extractBrightAreas(commandBuffer: MTLCommandBuffer, inputTexture: MTLTexture, outputTexture: MTLTexture) {
        // Create render pass descriptor
        let renderPassDescriptor = MTLRenderPassDescriptor()
        renderPassDescriptor.colorAttachments[0].texture = outputTexture
        renderPassDescriptor.colorAttachments[0].loadAction = .clear
        renderPassDescriptor.colorAttachments[0].clearColor = MTLClearColor(red: 0, green: 0, blue: 0, alpha: 1)
        renderPassDescriptor.colorAttachments[0].storeAction = .store

        // Create render command encoder
        guard let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor),
              let thresholdPipelineState = thresholdPipelineState,
              let quadVertexBuffer = quadVertexBuffer else {
            return
        }

        // Set render pipeline state
        renderEncoder.setRenderPipelineState(thresholdPipelineState)

        // Set vertex buffer
        renderEncoder.setVertexBuffer(quadVertexBuffer, offset: 0, index: 0)

        // Set fragment texture
        renderEncoder.setFragmentTexture(inputTexture, index: 0)

        // 根据黑柔强度动态调整阈值，强度越高，提取更多亮部区域以增强扩散
        var dynamicThreshold = thresholdValue * (1.0 - bloomIntensity * 0.3)  // 当强度增加时，适当降低阈值
        dynamicThreshold = max(0.01, dynamicThreshold)  // 确保阈值不会太低

        // Set dynamic threshold value
        renderEncoder.setFragmentBytes(&dynamicThreshold, length: MemoryLayout<Float>.size, index: 0)

        // Draw quad
        renderEncoder.drawPrimitives(type: .triangleStrip, vertexStart: 0, vertexCount: 4)

        // End encoding
        renderEncoder.endEncoding()
    }

    // Multi-level blur for enhanced diffusion effect
    private func blurTextureMultiLevel(commandBuffer: MTLCommandBuffer, inputTexture: MTLTexture, intermediateTexture: MTLTexture, outputTexture: MTLTexture) {
        // 根据强度计算多级模糊参数
        // 基础模糊半径更大，扩散范围更广
        let baseSigma: Float = 12.0  // 增加基础模糊半径
        let maxSigma: Float = 40.0   // 大幅增加最大模糊半径，从24增加到40
        let firstPassSigma = baseSigma + (bloomIntensity * (maxSigma - baseSigma))

        // 第二级模糊使用更大的半径，产生更广的扩散
        let secondPassSigma = firstPassSigma * 1.5  // 第二级比第一级大50%

        // 第一次模糊：中等范围的扩散
        let firstBlur = MPSImageGaussianBlur(device: device, sigma: firstPassSigma)
        firstBlur.encode(commandBuffer: commandBuffer, sourceTexture: inputTexture, destinationTexture: intermediateTexture)

        // 第二次模糊：在第一次的基础上进行更大范围的扩散
        let secondBlur = MPSImageGaussianBlur(device: device, sigma: secondPassSigma)
        secondBlur.encode(commandBuffer: commandBuffer, sourceTexture: intermediateTexture, destinationTexture: outputTexture)
    }

    // 保留原始的单级模糊方法，用于其他可能的用途
    private func blurTexture(commandBuffer: MTLCommandBuffer, inputTexture: MTLTexture, outputTexture: MTLTexture) {
        // Create Gaussian blur kernel with sigma value that scales with bloomIntensity
        // This makes the blur radius (diffusion area) increase as the Mist intensity increases
        let baseSigma: Float = 12.0  // 增加基础模糊半径
        let dynamicSigma = baseSigma + (bloomIntensity * 28.0) // 扩大范围从12到40

        let blur = MPSImageGaussianBlur(device: device, sigma: dynamicSigma)

        // Encode blur
        blur.encode(commandBuffer: commandBuffer, sourceTexture: inputTexture, destinationTexture: outputTexture)
    }

    // Combine original and blurred textures
    private func combineTextures(commandBuffer: MTLCommandBuffer, originalTexture: MTLTexture, blurredTexture: MTLTexture, outputTexture: MTLTexture) {
        // Create render pass descriptor
        let renderPassDescriptor = MTLRenderPassDescriptor()
        renderPassDescriptor.colorAttachments[0].texture = outputTexture
        renderPassDescriptor.colorAttachments[0].loadAction = .clear
        renderPassDescriptor.colorAttachments[0].clearColor = MTLClearColor(red: 0, green: 0, blue: 0, alpha: 1)
        renderPassDescriptor.colorAttachments[0].storeAction = .store

        // Create render command encoder
        guard let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor),
              let bloomPipelineState = bloomPipelineState,
              let quadVertexBuffer = quadVertexBuffer else {
            return
        }

        // Set render pipeline state
        renderEncoder.setRenderPipelineState(bloomPipelineState)

        // Set vertex buffer
        renderEncoder.setVertexBuffer(quadVertexBuffer, offset: 0, index: 0)

        // Set fragment textures
        renderEncoder.setFragmentTexture(originalTexture, index: 0)
        renderEncoder.setFragmentTexture(blurredTexture, index: 1)

        // Set bloom intensity
        renderEncoder.setFragmentBytes(&bloomIntensity, length: MemoryLayout<Float>.size, index: 0)

        // Set brightness value
        renderEncoder.setFragmentBytes(&brightnessValue, length: MemoryLayout<Float>.size, index: 1)

        // Set tint values (RGB)
        renderEncoder.setFragmentBytes(&redTintValue, length: MemoryLayout<Float>.size, index: 2)
        renderEncoder.setFragmentBytes(&greenTintValue, length: MemoryLayout<Float>.size, index: 3)
        renderEncoder.setFragmentBytes(&blueTintValue, length: MemoryLayout<Float>.size, index: 4)

        // Draw quad
        renderEncoder.drawPrimitives(type: .triangleStrip, vertexStart: 0, vertexCount: 4)

        // End encoding
        renderEncoder.endEncoding()
    }

    // 获取命令队列
    func getCommandQueue() -> MTLCommandQueue? {
        return commandQueue
    }

    // Cleanup resources when the renderer is deallocated
    deinit {
        cleanupTextures()
        print("MetalRenderer deallocated and cleaned up")
    }
}
