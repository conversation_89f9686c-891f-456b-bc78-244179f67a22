//
//  Shaders.metal
//  TrueFilm
//
//  Created by Augment on 2025/4/12.
//

#include <metal_stdlib>
using namespace metal;

// Vertex shader input and output structures
typedef struct {
    float4 position [[position]];
    float2 texCoord;
} VertexOutput;

// Basic passthrough vertex shader
vertex VertexOutput vertexShader(uint vertexID [[vertex_id]],
                               constant float4 *vertices [[buffer(0)]]) {
    VertexOutput out;
    out.position = float4(vertices[vertexID].xy, 0.0, 1.0);
    out.texCoord = vertices[vertexID].zw;
    return out;
}

// Basic fragment shader that just outputs the texture with brightness adjustment
fragment float4 fragmentShader(VertexOutput in [[stage_in]],
                              texture2d<float> texture [[texture(0)]],
                              constant float &brightness [[buffer(0)]]) {
    constexpr sampler textureSampler(mag_filter::linear, min_filter::linear);
    float4 color = texture.sample(textureSampler, in.texCoord);

    // Apply brightness adjustment
    float3 result = color.rgb + brightness;

    // Clamp result to valid range
    result = max(float3(0.0), min(float3(1.0), result));

    return float4(result, color.a);
}

// Threshold shader for extracting bright areas
fragment float4 thresholdShader(VertexOutput in [[stage_in]],
                               texture2d<float> texture [[texture(0)]],
                               constant float &threshold [[buffer(0)]]) {
    constexpr sampler textureSampler(mag_filter::linear, min_filter::linear);
    float4 color = texture.sample(textureSampler, in.texCoord);

    // Calculate luminance
    float luminance = dot(color.rgb, float3(0.2126, 0.7152, 0.0722));

    // Apply threshold
    if (luminance > threshold) {
        return color;
    } else {
        return float4(0, 0, 0, 1);
    }
}

// Bloom effect shader
fragment float4 bloomShader(VertexOutput in [[stage_in]],
                           texture2d<float> originalTexture [[texture(0)]],
                           texture2d<float> blurredTexture [[texture(1)]],
                           constant float &intensity [[buffer(0)]],
                           constant float &brightness [[buffer(1)]],
                           constant float &redTint [[buffer(2)]],
                           constant float &greenTint [[buffer(3)]],
                           constant float &blueTint [[buffer(4)]]) {
    constexpr sampler textureSampler(mag_filter::linear, min_filter::linear);
    float4 originalColor = originalTexture.sample(textureSampler, in.texCoord);
    float4 blurredColor = blurredTexture.sample(textureSampler, in.texCoord);

    // 增强模糊纹理的亮度，强度随intensity参数动态调整
    float brightnessFactor = 1.5 + (intensity * 0.5); // 随着intensity增加而增加亮度
    float3 brightBlur = blurredColor.rgb * brightnessFactor;
    
    // 保持中性灰度效果
    float blurLuminance = dot(brightBlur, float3(0.2126, 0.7152, 0.0722));
    // 创建灰度版本的模糊效果
    float enhanceFactor = 1.2 + (intensity * 0.3); // 随着intensity增加而增强扩散效果
    float3 neutralBlur = float3(blurLuminance, blurLuminance, blurLuminance) * enhanceFactor;

    // Apply a screen blend mode for a softer effect
    // Screen blend: 1 - (1 - a) * (1 - b)
    float3 screen = float3(1.0) - (float3(1.0) - originalColor.rgb) * (float3(1.0) - neutralBlur * intensity);

    // 增加混合因子，并使其随intensity动态调整
    // 当intensity越高时，混合比例越高，扩散效果越明显
    float blendFactor = 0.7 + (intensity * 0.3); // 从0.7到1.0，随intensity增加
    float3 result = mix(originalColor.rgb, screen, blendFactor);

    // 亮度调整，随intensity略微改变
    float brightnessBoost = 1.1 + (intensity * 0.05);
    result = result * brightnessBoost;

    // Apply brightness adjustment
    result = result + brightness;

    result = max(float3(0.0), min(float3(1.0), result));

    return float4(result, originalColor.a);
}

// Halation effect shader
fragment float4 halationShader(VertexOutput in [[stage_in]],
                              texture2d<float> originalTexture [[texture(0)]],
                              texture2d<float> blurredTexture [[texture(1)]],
                              constant float &intensity [[buffer(0)]]) {
    constexpr sampler textureSampler(mag_filter::linear, min_filter::linear);
    float4 originalColor = originalTexture.sample(textureSampler, in.texCoord);
    float4 blurredColor = blurredTexture.sample(textureSampler, in.texCoord);

    // Calculate luminance for a neutral effect instead of color tint
    float luminance = dot(blurredColor.rgb, float3(0.2126, 0.7152, 0.0722));
    float3 neutralBlur = float3(luminance);
    
    // Use lighten blend mode (max) to preserve the black softness effect
    float3 result = max(originalColor.rgb, neutralBlur * intensity);

    return float4(result, originalColor.a);
}