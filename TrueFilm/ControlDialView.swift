//
//  ControlDialView.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/14.
//

import SwiftUI

// 齿轮边缘绘制器 - 使用单一路径代替多个小路径，提高性能
struct SerratedDialEdge: Shape {
    let center: CGPoint
    let innerRadius: CGFloat
    let outerRadius: CGFloat
    let teethCount: Int

    func path(in rect: CGRect) -> Path {
        var path = Path()

        // 使用更少的齿数来提高性能
        for i in 0..<teethCount {
            // 计算角度 - 为每个齿留出更宽的空间
            let startAngle = Double(i) * (2 * .pi / Double(teethCount))
            let endAngle = Double(i + 1) * (2 * .pi / Double(teethCount))
            let midAngle = (startAngle + endAngle) / 2

            // 起始点
            let startX = center.x + innerRadius * CGFloat(cos(startAngle))
            let startY = center.y + innerRadius * CGFloat(sin(startAngle))

            if i == 0 {
                path.move(to: CGPoint(x: startX, y: startY))
            }

            // 可乐瓶盖风格的圆润宽锯齿 - 更像是一块一块的而不是尖尖的
            let toothHeight = (outerRadius - innerRadius) * 0.65  // 降低高度

            // 凸起部分的宽度占比
            let toothWidthRatio = 0.80  // 齿宽占整个齿间距的70%

            // 计算凸起部分的开始和结束角度
            let toothStartAngle = startAngle + (1.0 - toothWidthRatio) * (endAngle - startAngle) * 0.5
            let toothEndAngle = endAngle - (1.0 - toothWidthRatio) * (endAngle - startAngle) * 0.5

            // 凸起部分的左侧点
            let leftX = center.x + (innerRadius + toothHeight * 0.3) * CGFloat(cos(toothStartAngle))
            let leftY = center.y + (innerRadius + toothHeight * 0.3) * CGFloat(sin(toothStartAngle))

            // 定义光滑圆润的齿 - 使用光滑的贝塞尔曲线

            // 凸起的过渡点更圆润 - 增加过渡率
            let transitionFactor = 0.4 // 过渡因子，越大越圆润

            // 凸起部分的左侧移动控制点 - 使曲线出入更加圆润
            let leftInnerControlX = center.x + (innerRadius + toothHeight * transitionFactor) * CGFloat(cos(toothStartAngle))
            let leftInnerControlY = center.y + (innerRadius + toothHeight * transitionFactor) * CGFloat(sin(toothStartAngle))

            // 增加更光滑的中间控制点
            let leftControlX = center.x + (innerRadius + toothHeight * 0.8) * CGFloat(cos(toothStartAngle + 0.08))
            let leftControlY = center.y + (innerRadius + toothHeight * 0.8) * CGFloat(sin(toothStartAngle + 0.08))

            // 左边顶部点 - 增加更光滑的曲线
            let leftPeakX = center.x + (innerRadius + toothHeight * 0.95) * CGFloat(cos(toothStartAngle + (toothEndAngle - toothStartAngle) * 0.25))
            let leftPeakY = center.y + (innerRadius + toothHeight * 0.95) * CGFloat(sin(toothStartAngle + (toothEndAngle - toothStartAngle) * 0.25))

            // 顶部控制点 - 增加升高使顶部更光滑
            let topLeftControlX = center.x + (innerRadius + toothHeight * 1.0) * CGFloat(cos(midAngle - (toothEndAngle - toothStartAngle) * 0.2))
            let topLeftControlY = center.y + (innerRadius + toothHeight * 1.0) * CGFloat(sin(midAngle - (toothEndAngle - toothStartAngle) * 0.2))

            // 中间点 - 稍微扩大半径使顶部创建圆润弧形
            let topX = center.x + (innerRadius + toothHeight * 1.05) * CGFloat(cos(midAngle))
            let topY = center.y + (innerRadius + toothHeight * 1.05) * CGFloat(sin(midAngle))

            // 顶部右侧控制点
            let topRightControlX = center.x + (innerRadius + toothHeight * 1.0) * CGFloat(cos(midAngle + (toothEndAngle - toothStartAngle) * 0.2))
            let topRightControlY = center.y + (innerRadius + toothHeight * 1.0) * CGFloat(sin(midAngle + (toothEndAngle - toothStartAngle) * 0.2))

            // 右边顶部点
            let rightPeakX = center.x + (innerRadius + toothHeight * 0.95) * CGFloat(cos(toothEndAngle - (toothEndAngle - toothStartAngle) * 0.25))
            let rightPeakY = center.y + (innerRadius + toothHeight * 0.95) * CGFloat(sin(toothEndAngle - (toothEndAngle - toothStartAngle) * 0.25))

            // 右侧控制点
            let rightControlX = center.x + (innerRadius + toothHeight * 0.8) * CGFloat(cos(toothEndAngle - 0.08))
            let rightControlY = center.y + (innerRadius + toothHeight * 0.8) * CGFloat(sin(toothEndAngle - 0.08))

            // 右侧内部控制点
            let rightInnerControlX = center.x + (innerRadius + toothHeight * transitionFactor) * CGFloat(cos(toothEndAngle))
            let rightInnerControlY = center.y + (innerRadius + toothHeight * transitionFactor) * CGFloat(sin(toothEndAngle))

            // 凸起部分的右侧点
            let rightX = center.x + (innerRadius + toothHeight * 0.3) * CGFloat(cos(toothEndAngle))
            let rightY = center.y + (innerRadius + toothHeight * 0.3) * CGFloat(sin(toothEndAngle))

            // 结束点
            let endX = center.x + innerRadius * CGFloat(cos(endAngle))
            let endY = center.y + innerRadius * CGFloat(sin(endAngle))

            // 使用贝塞尔曲线绘制更圆润的路径

            // 起点到左侧过渡点，使用内部控制点创造圆润过渡
            path.addQuadCurve(to: CGPoint(x: leftX, y: leftY), control: CGPoint(x: leftInnerControlX, y: leftInnerControlY))

            // 从左侧过渡到左侧顶部，使用控制点创造圆润上升
            path.addQuadCurve(to: CGPoint(x: leftPeakX, y: leftPeakY), control: CGPoint(x: leftControlX, y: leftControlY))

            // 从左侧顶部到顶部中间点，使用控制点创造光滑弧形
            path.addQuadCurve(to: CGPoint(x: topX, y: topY), control: CGPoint(x: topLeftControlX, y: topLeftControlY))

            // 从顶部中间到右侧顶部，使用控制点创造光滑弧形
            path.addQuadCurve(to: CGPoint(x: rightPeakX, y: rightPeakY), control: CGPoint(x: topRightControlX, y: topRightControlY))

            // 从右侧顶部到右侧过渡点，使用控制点创造圆润下降
            path.addQuadCurve(to: CGPoint(x: rightX, y: rightY), control: CGPoint(x: rightControlX, y: rightControlY))

            // 从右侧过渡到结束点，使用内部控制点创造圆润过渡
            path.addQuadCurve(to: CGPoint(x: endX, y: endY), control: CGPoint(x: rightInnerControlX, y: rightInnerControlY))
        }

        // 添加内圈完成路径
        path.addArc(center: center, radius: innerRadius, startAngle: .radians(2 * .pi), endAngle: .radians(0), clockwise: true)

        return path
    }
}

// 同心圆绘制器 - 使用单一形状代替多个循环
struct ConcentricCircles: Shape {
    let center: CGPoint
    let radius: CGFloat
    let count: Int

    func path(in rect: CGRect) -> Path {
        var path = Path()

        // 绘制多个同心圆
        for i in 1...count {
            let circleRadius = radius * CGFloat(i * 3) / CGFloat(count * 4)
            path.addEllipse(in: CGRect(
                x: center.x - circleRadius,
                y: center.y - circleRadius,
                width: circleRadius * 2,
                height: circleRadius * 2
            ))
        }

        return path
    }
}

struct ControlDialView: View {
    // Bound properties
    @Binding var dialRotation: Double
    @Binding var isSnapping: Bool

    // UI mode
    var isWhiteMode: Bool = UserDefaults.standard.bool(forKey: "whiteUIMode")

    // 按钮音效设置
    @State private var buttonSoundsEnabled: Bool = UserDefaults.standard.bool(forKey: "buttonSoundsEnabled")

    // Add a state variable to force view refresh when language changes
    @State private var languageRefreshID = UUID()

    // 锁定状态 - 当为true时，转盘不能旋转
    var isLocked: Bool = false

    // 限位参数 - 齿轮的最小和最大旋转角度限制
    var minRotation: Double = -Double.infinity
    var maxRotation: Double = Double.infinity
    var hasLimits: Bool = false

    // Callbacks
    var onRotationChanged: (Double) -> Void
    var onRotationEnded: () -> Void
    var onDoubleTap: () -> Void
    var onTap: () -> Void

    // State
    @State private var lastRotation: Double = 0
    @State private var isPressed: Bool = false
    @State private var reachedLimit: Bool = false

    // Haptic feedback - 表冠式震动反馈
    private let crownHapticFeedback = UIImpactFeedbackGenerator(style: .light)  // 表冠式轻量震动
    private let takePhotoFeedback = UIImpactFeedbackGenerator(style: .rigid)
    private let limitFeedback = UIImpactFeedbackGenerator(style: .rigid)  // 触碰到极限值时的反馈

    // 表冠齿轮跳动状态追踪 - 基于参数值变化而非物理齿轮
    @State private var lastParameterStep: Int = 0  // 上一个参数步进位置

    init(
        dialRotation: Binding<Double>,
        isSnapping: Binding<Bool>,
        isWhiteMode: Bool = UserDefaults.standard.bool(forKey: "whiteUIMode"),
        isLocked: Bool = false,
        minRotation: Double = -Double.infinity,
        maxRotation: Double = Double.infinity,
        onRotationChanged: @escaping (Double) -> Void,
        onRotationEnded: @escaping () -> Void,
        onDoubleTap: @escaping () -> Void,
        onTap: @escaping () -> Void = {}
    ) {
        self._dialRotation = dialRotation
        self._isSnapping = isSnapping
        self.isWhiteMode = isWhiteMode
        self.isLocked = isLocked
        self.minRotation = minRotation
        self.maxRotation = maxRotation
        self.hasLimits = minRotation > -Double.infinity || maxRotation < Double.infinity
        self.onRotationChanged = onRotationChanged
        self.onRotationEnded = onRotationEnded
        self.onDoubleTap = onDoubleTap
        self.onTap = onTap

        // Initialize lastRotation from the current dialRotation
        self._lastRotation = State(initialValue: dialRotation.wrappedValue)

        // Initialize parameter step based on current rotation (100 steps for 0.01 precision)
        // 假设参数范围是0-1，每0.01为一步，共100步
        let parameterStepsPerRotation = 100.0  // 每360度100步，即每3.6度一步
        let normalizedRotation = dialRotation.wrappedValue.truncatingRemainder(dividingBy: 360)
        let positiveNormalizedRotation = normalizedRotation < 0 ? normalizedRotation + 360 : normalizedRotation
        let initialParameterStep = Int((positiveNormalizedRotation / 360.0 * parameterStepsPerRotation).rounded())
        self._lastParameterStep = State(initialValue: initialParameterStep)
    }

    var body: some View {
        GeometryReader { geometry in
            let center = CGPoint(x: geometry.size.width/2, y: geometry.size.height/2)
            let dialRadius: CGFloat = min(geometry.size.width, geometry.size.height)/2 - 10

            ZStack {
                // Main dial body (black in white mode, silver/metallic in black mode)
                Circle()
                    .fill(
                        isWhiteMode
                        ? LinearGradient(
                            gradient: Gradient(colors: [Color.black.opacity(0.95), Color.black.opacity(0.85), Color.black.opacity(0.9)]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                          )
                        : LinearGradient(
                            gradient: Gradient(colors: [Color.gray.opacity(0.9), Color.white.opacity(0.95), Color.gray.opacity(0.85)]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                          )
                    )
                    .frame(width: dialRadius * 2, height: dialRadius * 2)
                    .position(center)
                    .shadow(color: Color.black.opacity(0.3), radius: 4, x: 0, y: 2)
                    .scaleEffect(isPressed ? 0.97 : 1.0)

                // 使用单一的形状替代多个循环
                ConcentricCircles(center: center, radius: dialRadius, count: 3)
                    .stroke(isWhiteMode ? Color.gray.opacity(0.2) : Color.gray.opacity(0.1), lineWidth: 0.5)
                    .scaleEffect(isPressed ? 0.97 : 1.0)

                // Center highlight to simulate subtle reflection
                Circle()
                    .fill(
                        RadialGradient(
                            gradient: Gradient(colors: [
                                isWhiteMode
                                ? Color.gray.opacity(0.2)
                                : Color.white.opacity(0.6),
                                Color.clear
                            ]),
                            center: .center,
                            startRadius: 0,
                            endRadius: dialRadius * 0.5
                        )
                    )
                    .frame(width: dialRadius * 1.2, height: dialRadius * 1.2)
                    .position(center)
                    .scaleEffect(isPressed ? 0.97 : 1.0)
                    .blendMode(.screen)

                // 使用单一路径绘制齿轮边缘，而不是多个ForEach
                SerratedDialEdge(center: center, innerRadius: dialRadius - 2, outerRadius: dialRadius + 8, teethCount: 18)
                    .fill(
                        isWhiteMode
                        ? LinearGradient(
                            gradient: Gradient(colors: [Color.black.opacity(0.9), Color.black.opacity(0.85), Color.black.opacity(0.9)]),
                            startPoint: .top,
                            endPoint: .bottom
                          )
                        : LinearGradient(
                            gradient: Gradient(colors: [Color.gray.opacity(0.9), Color.white.opacity(0.95), Color.gray.opacity(0.85)]),
                            startPoint: .top,
                            endPoint: .bottom
                          )
                    )
                    .shadow(color: Color.black.opacity(0.15), radius: 0.5, x: 0, y: 0.5)
                    .scaleEffect(isPressed ? 0.97 : 1.0)
            }
            .frame(width: geometry.size.width, height: geometry.size.height)
            .rotationEffect(.degrees(dialRotation), anchor: .center)
            .animation(.none, value: isPressed)
        }
        .aspectRatio(1, contentMode: .fit)
        .frame(width: 130, height: 130)
        // 完全禁用动画以提高性能
        .animation(nil, value: dialRotation)
        // Add ID to force refresh when language changes
        .id("control_dial_\(languageRefreshID)")
        .gesture(
            DragGesture(minimumDistance: 0.1)
                .onChanged { value in
                    // 如果转盘被锁定，不处理拖动手势
                    guard !isLocked else { return }

                    let center = CGPoint(x: 65, y: 65)
                    let location = value.location

                    // 计算角度
                    let angle = atan2(location.y - center.y, location.x - center.x)
                    let angleDegrees = angle * 180 / .pi + 90

                    // 计算旋转差值
                    let delta = angleDegrees - lastRotation

                    // 处理边界情况 - 防止在0/360度交界处突然跳跃
                    var adjustedDelta = delta
                    if delta > 180 {
                        adjustedDelta -= 360
                    } else if delta < -180 {
                        adjustedDelta += 360
                    }

                    // 计算新的旋转角度（带灵敏度）
                    let sensitivity: Double = 1.0  // 旋转灵敏度
                    let newRotation = dialRotation + adjustedDelta * sensitivity

                    // 处理限位检查与角度环绕问题
                    var clampedRotation = newRotation
                    var limitReached = false

                    // 如果当前旋转方向会导致从接近最大值跳到最小值，则阻止这种跳变
                    let normalizedNew = newRotation.truncatingRemainder(dividingBy: 360)
                    let normalizedCurrent = dialRotation.truncatingRemainder(dividingBy: 360)

                    // 检测是否有跳变的危险（从接近360跳到0）
                    let isWrappingAround = normalizedCurrent > 350 && normalizedNew < 10 && adjustedDelta > 0

                    if hasLimits {
                        // 确保旋转角度不会超过最小值和最大值
                        if newRotation < minRotation {
                            // 完全限制在最小值
                            clampedRotation = minRotation

                            // 如果之前不在限位状态，则提供触觉反馈
                            if !reachedLimit {
                                limitFeedback.impactOccurred(intensity: 1.0)
                                reachedLimit = true
                            }
                            limitReached = true
                        } else if newRotation > maxRotation || isWrappingAround {
                            // 完全限制在最大值 - 注意添加了isWrappingAround条件
                            clampedRotation = maxRotation

                            // 如果之前不在限位状态，则提供触觉反馈
                            if !reachedLimit {
                                limitFeedback.impactOccurred(intensity: 1.0)
                                reachedLimit = true
                            }
                            limitReached = true
                        } else {
                            // 不在限位范围内，可以自由旋转
                            reachedLimit = false
                        }
                    }

                    // 始终更新旋转角度，即使到达限位也更新为限位值
                    dialRotation = clampedRotation
                    print("ControlDialView: 调用 onRotationChanged 传递旋转角度 \(dialRotation)")
                    onRotationChanged(dialRotation)

                    // 如果没有到达限位，提供表冠式参数步进触觉反馈
                    if !limitReached {
                        // 表冠式触觉反馈 - 基于参数值的0.01步进（100步/360度）
                        let parameterStepsPerRotation = 100.0  // 每360度100步，对应0.01精度

                        // 标准化旋转角度到0-360度
                        let normalizedRotation = dialRotation.truncatingRemainder(dividingBy: 360)
                        let positiveNormalizedRotation = normalizedRotation < 0 ? normalizedRotation + 360 : normalizedRotation

                        // 计算当前参数步进位置
                        let currentParameterStep = Int((positiveNormalizedRotation / 360.0 * parameterStepsPerRotation).rounded())

                        // 检查是否跨越了参数步进边界
                        if currentParameterStep != lastParameterStep {
                            // 表冠式轻量但清晰的震动反馈 - 每0.01参数变化一次震动
                            crownHapticFeedback.impactOccurred(intensity: 0.5)
                            lastParameterStep = currentParameterStep
                        }
                    }

                    lastRotation = angleDegrees
                }
                .onEnded { _ in
                    // 如果转盘被锁定，不处理拖动结束手势
                    guard !isLocked else { return }

                    // 结束时提供轻微的表冠式反馈
                    crownHapticFeedback.impactOccurred(intensity: 0.4)

                    // 调用旋转结束的回调
                    onRotationEnded()

                    // Reset last rotation for next drag to avoid jumps - fixed value reference error
                    lastRotation = dialRotation.truncatingRemainder(dividingBy: 360)

                    // 更新参数步进位置以保持同步
                    let parameterStepsPerRotation = 100.0
                    let normalizedRotation = dialRotation.truncatingRemainder(dividingBy: 360)
                    let positiveNormalizedRotation = normalizedRotation < 0 ? normalizedRotation + 360 : normalizedRotation
                    lastParameterStep = Int((positiveNormalizedRotation / 360.0 * parameterStepsPerRotation).rounded())
                }
        )
        .simultaneousGesture(
            TapGesture()
                .onEnded {
                    // 无论是否锁定，点击转盘都触发拍照
                    withAnimation {
                        isPressed = true
                    }
                    takePhotoFeedback.impactOccurred(intensity: 1.0)
                    onTap()

                    // 延迟释放按下状态
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                        withAnimation {
                            isPressed = false
                        }
                    }
                }
        )
        .simultaneousGesture(
            LongPressGesture(minimumDuration: 0.5)
                .onChanged { _ in
                    // 如果转盘被锁定，不处理长按手势
                    guard !isLocked else { return }

                    if !isPressed {
                        crownHapticFeedback.impactOccurred(intensity: 0.6)
                        withAnimation {
                            isPressed = true
                        }
                    }
                }
                .onEnded { _ in
                    // 如果转盘被锁定，不处理长按结束手势
                    guard !isLocked else {
                        // 释放按下状态
                        withAnimation {
                            isPressed = false
                        }
                        return
                    }

                    // 长按重置当前预设属性
                    onDoubleTap()

                    // 提供比较强的触觉反馈表示重置成功
                    let generator = UIImpactFeedbackGenerator(style: .heavy)
                    generator.impactOccurred(intensity: 1.0)

                    withAnimation {
                        isPressed = false
                    }
                }
        )
        .onTapGesture(count: 2) {
            // 如果转盘被锁定，不处理双击手势
            guard !isLocked else { return }

            // Double-tap to reset - 保留此功能以兼容现有逻辑
            onDoubleTap()
            crownHapticFeedback.impactOccurred(intensity: 0.8)
        }
        .onAppear {
            // Prepare haptic feedback - 表冠式震动反馈
            crownHapticFeedback.prepare()
            takePhotoFeedback.prepare()
            limitFeedback.prepare()

            // Subscribe to language change notifications
            NotificationCenter.default.addObserver(forName: .languageDidChange, object: nil, queue: .main) { _ in
                print("ControlDialView: 检测到语言变更，刷新UI")
                // Force refresh by updating the ID
                self.languageRefreshID = UUID()
            }

            // 监听按钮音效设置变更
            NotificationCenter.default.addObserver(forName: NSNotification.Name("ButtonSoundsChanged"), object: nil, queue: .main) { [self] _ in
                buttonSoundsEnabled = UserDefaults.standard.bool(forKey: "buttonSoundsEnabled")
                print("[ControlDialView] 按钮音效设置已更新: \(buttonSoundsEnabled ? "开启" : "关闭")")
            }
        }
        .onDisappear {
            // Remove notification observers
            NotificationCenter.default.removeObserver(self)
        }
    }
}

#Preview {
    struct PreviewWrapper: View {
        @State private var rotation: Double = 90
        @State private var isSnapping: Bool = false

        var body: some View {
            ZStack {
                Color.black.edgesIgnoringSafeArea(.all)

                ControlDialView(
                    dialRotation: $rotation,
                    isSnapping: $isSnapping,
                    onRotationChanged: { newRotation in
                        print("Rotation changed: \(newRotation)")
                    },
                    onRotationEnded: {
                        isSnapping = true
                        // Simulate snapping effect
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            isSnapping = false
                        }
                    },
                    onDoubleTap: {
                        rotation = 90
                    }
                )
            }
        }
    }

    return PreviewWrapper()
}