//
//  GalleryViews.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/22.
//

import SwiftUI
import Foundation
import UIKit
import AVKit
import Combine
import ImageIO

// 缩略图缓存管理器
class ThumbnailCache {
    static let shared = ThumbnailCache()
    private let cache = NSCache<NSString, UIImage>()
    private let operationQueue = OperationQueue()
    
    private init() {
        // 配置缓存
        cache.countLimit = 100 // 最多缓存100个缩略图
        cache.totalCostLimit = 100 * 1024 * 1024 // 100MB内存限制
        
        // 配置操作队列
        operationQueue.maxConcurrentOperationCount = 4 // 最多4个并发操作
        operationQueue.qualityOfService = .userInitiated
        
        // 监听内存警告
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(clearCache),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    @objc private func clearCache() {
        cache.removeAllObjects()
        operationQueue.cancelAllOperations()
        print("[缩略图缓存] 收到内存警告，已清理缓存")
    }
    
    // 公共清理方法
    func clearAllThumbnails() {
        cache.removeAllObjects()
        operationQueue.cancelAllOperations()
        print("[缩略图缓存] 手动清理缓存")
    }
    
    // 获取缩略图（同步版本，用于快速显示）
    func getThumbnail(for image: UIImage, size: CGSize) -> UIImage? {
        let key = generateKey(for: image, size: size)
        
        // 先检查缓存
        if let cachedThumbnail = cache.object(forKey: key as NSString) {
            return cachedThumbnail
        }
        
        // 如果没有缓存，生成缩略图
        let thumbnail = generateThumbnail(from: image, size: size)
        
        // 缓存结果
        if let thumbnail = thumbnail {
            cache.setObject(thumbnail, forKey: key as NSString)
        }
        
        return thumbnail
    }
    
    // 异步预生成缩略图
    func preloadThumbnail(for image: UIImage, size: CGSize, completion: @escaping (UIImage?) -> Void) {
        let key = generateKey(for: image, size: size)
        
        // 检查是否已缓存
        if let cachedThumbnail = cache.object(forKey: key as NSString) {
            DispatchQueue.main.async {
                completion(cachedThumbnail)
            }
            return
        }
        
        // 添加到操作队列异步生成
        operationQueue.addOperation {
            let thumbnail = self.generateThumbnail(from: image, size: size)
            
            if let thumbnail = thumbnail {
                self.cache.setObject(thumbnail, forKey: key as NSString)
            }
            
            DispatchQueue.main.async {
                completion(thumbnail)
            }
        }
    }
    
    private func generateKey(for image: UIImage, size: CGSize) -> String {
        return "thumb_\(image.hashValue)_\(Int(size.width))x\(Int(size.height))"
    }
    
    private func generateThumbnail(from image: UIImage, size: CGSize) -> UIImage? {
        // 使用高效的图形上下文生成缩略图
        let targetSize = calculateOptimalSize(for: image.size, targetSize: size)
        
        UIGraphicsBeginImageContextWithOptions(targetSize, false, 0.0)
        defer { UIGraphicsEndImageContext() }
        
        image.draw(in: CGRect(origin: .zero, size: targetSize))
        return UIGraphicsGetImageFromCurrentImageContext()
    }
    
    private func calculateOptimalSize(for imageSize: CGSize, targetSize: CGSize) -> CGSize {
        let widthRatio = targetSize.width / imageSize.width
        let heightRatio = targetSize.height / imageSize.height
        let scaleFactor = min(widthRatio, heightRatio)
        
        return CGSize(
            width: imageSize.width * scaleFactor,
            height: imageSize.height * scaleFactor
        )
    }
}

// 媒体类型
enum MediaType: Equatable {
    case photo(UIImage)
    case video(URL)
    
    // 实现Equatable协议的相等判断
    static func == (lhs: MediaType, rhs: MediaType) -> Bool {
        switch (lhs, rhs) {
        case (.photo(let lhsImage), .photo(let rhsImage)):
            // 图片比较：使用内存地址比较，因为图片内容比较是昂贵的
            return lhsImage === rhsImage
        case (.video(let lhsURL), .video(let rhsURL)):
            // 视频比较：使用URL路径比较
            return lhsURL == rhsURL
        default:
            // 不同类型不相等
            return false
        }
    }
    
    var thumbnail: UIImage? {
        switch self {
        case .photo(let image):
            // 生成并缓存缩略图以提高性能
            return ThumbnailCache.shared.getThumbnail(for: image, size: CGSize(width: 200, height: 200))
        case .video(let url):
            // 使用缓存优先策略
            return AppMediaStorage.shared.getCachedVideoThumbnail(for: url)
        }
    }
    
    var duration: TimeInterval? {
        switch self {
        case .photo(_):
            return nil
        case .video(let url):
            // 首先检查文件是否存在
            guard FileManager.default.fileExists(atPath: url.path) else {
                print("试图读取时长的视频文件不存在: \(url.path)")
                return nil
            }
            
            // 使用 AVAssetTrack 直接读取时长，更可靠
            let asset = AVAsset(url: url)
            
            // 使用同步方法加载时长属性，避免异步问题
            if asset.duration.seconds.isNaN || asset.duration.seconds == 0 {
                // 如果直接读取无效，尝试从视频附属轨道读取时长
                let tracks = asset.tracks(withMediaType: .video)
                if let videoTrack = tracks.first {
                    // 从轨道中获取时长
                    let trackDuration = videoTrack.timeRange.duration.seconds
                    if trackDuration > 0 {
                        return trackDuration
                    }
                }
                
                // 如果在音频轨道中查找
                let audioTracks = asset.tracks(withMediaType: .audio)
                if let audioTrack = audioTracks.first {
                    let audioLength = audioTrack.timeRange.duration.seconds
                    if audioLength > 0 {
                        return audioLength
                    }
                }
                
                // 还是读不到，测量文件大小来估计时长
                do {
                    let fileAttributes = try FileManager.default.attributesOfItem(atPath: url.path)
                    if let fileSize = fileAttributes[.size] as? Int64, fileSize > 1000 {
                        // 粗略估计: 假设每秒1MB文件大小
                        let estimatedDuration = max(Double(fileSize) / 1_000_000.0, 1.0)
                        print("基于文件大小估计视频时长: \(estimatedDuration) 秒")
                        return estimatedDuration 
                    }
                } catch {
                    print("无法读取文件属性: \(error.localizedDescription)")
                }
                
                print("无法获取视频时长，使用默认值")
                return 10.0  // 默认返回10秒
            }
            
            return asset.duration.seconds
        }
    }
}

// 视频播放器包装器 - 处理视频预加载和白屏问题
class VideoPlayerWrapper: NSObject, ObservableObject {
    @Published var player: AVPlayer?
    private var playerItem: AVPlayerItem?
    private var asset: AVAsset?
    private var isObserving = false
    private var isPlaying = false
    @Published var isLoading = true
    @Published var hasAudioTrack = false // 添加标记，指示视频是否有音频轨道
    
    // 播放器就绪回调
    var onReadyToPlay: (() -> Void)?
    var onLoadingChange: ((Bool) -> Void)?
    var onAudioStatusChange: ((Bool) -> Void)? // 添加音频状态回调
    
    deinit {
        cleanup()
        print("VideoPlayerWrapper 被释放")
    }
    
    // 初始化并开始加载视频
    func loadVideo(url: URL) -> AVPlayer {
        print("[视频加载] 开始加载视频: \(url.lastPathComponent)")
        
        // 清理旧的资源
        cleanup()
        
        // 设置加载状态
        isLoading = true
        if let onLoadingChange = onLoadingChange {
            onLoadingChange(true)
        }
        
        // 配置音频会话
        configureAudioSession()
        
        // 使用高质量设置创建资源 - 修复重复键
        let assetOptions = [
            AVURLAssetPreferPreciseDurationAndTimingKey: true
        ]
        
        asset = AVURLAsset(url: url, options: assetOptions)
        
        // 检查文件是否存在
        if !FileManager.default.fileExists(atPath: url.path) {
            print("[视频加载] 错误: 视频文件不存在 \(url.path)")
            isLoading = false
            onLoadingChange?(false)
            // 返回空播放器
            player = AVPlayer()
            return player!
        }
        
        // 预加载资源的关键属性，减少预加载特性以加快初始化速度
        let keys = ["playable"]
        playerItem = AVPlayerItem(asset: asset!, automaticallyLoadedAssetKeys: keys)
        
        // 预热音频渲染管线
        playerItem?.audioTimePitchAlgorithm = .spectral // 使用高质量音频算法
        
        // 创建播放器
        player = AVPlayer(playerItem: playerItem)
        
        // 关键设置来避免悬崖和白屏
        player?.actionAtItemEnd = .none // 防止播放结束时黑屏
        player?.allowsExternalPlayback = false // 防止外部播放导致的白屏
        player?.automaticallyWaitsToMinimizeStalling = false // 不等待缓冲过多
        
        // 设置音量 - 确保声音正常播放
        player?.volume = 1.0
        
        // 优化视频和音频质量
        if #available(iOS 15.0, *) {
            // iOS 15及以上版本支持
            player?.preventsDisplaySleepDuringVideoPlayback = true
        }
        
        // 设置播放优先级为高，确保系统分配足够资源进行音频处理
        player?.rate = 1.0
        
        // 检查和准备音频轨道
        if let audioTracks = asset?.tracks(withMediaType: .audio), !audioTracks.isEmpty {
            print("[视频加载] 发现音频轨道: \(audioTracks.count)个")
            
            // 详细诊断每个音频轨道的情况
            for (index, track) in audioTracks.enumerated() {
                // 获取基本音频信息，避免使用formatDescriptions
                print("[视频音频详情] 轨道\(index): 持续时间=\(track.timeRange.duration.seconds)秒")
                
                // 检查轨道是否可用
                print("[视频音频详情] 轨道\(index): 是否可播放=\(track.isPlayable), 是否自解码=\(track.isDecodable), 帧率=\(track.nominalFrameRate)")
            }
            hasAudioTrack = true
            onAudioStatusChange?(true)
        } else {
            print("[视频加载] 警告: 未发现音频轨道，此视频可能没有录制声音")
            
            // 尝试输出文件信息以进一步诊断
            print("[视频诊断] 文件大小: \(getFileSize(url: url)) 字节")
            print("[视频诊断] 文件格式: \(url.pathExtension)")
            
            // 尝试使用AVAsset获取更多信息 - 修复availableMediaTypes不存在的问题
            let videoTrackCount = asset?.tracks(withMediaType: .video).count ?? 0
            let audioTrackCount = asset?.tracks(withMediaType: .audio).count ?? 0
            let textTrackCount = asset?.tracks(withMediaType: .text).count ?? 0
            let metadataTrackCount = asset?.tracks(withMediaType: .metadata).count ?? 0
            
            print("[视频诊断] 视频轨道: \(videoTrackCount), 音频轨道: \(audioTrackCount), 文本轨道: \(textTrackCount), 元数据轨道: \(metadataTrackCount)")
            
            hasAudioTrack = false
            onAudioStatusChange?(false)
        }
        
        // 更积极的预加载设置
        playerItem?.preferredForwardBufferDuration = 2.0 // 减少预加载时间以加快初始显示
        
        // 添加观察者 - 使用更完善的选项
        playerItem?.addObserver(self, forKeyPath: "loadedTimeRanges", options: [.new, .initial], context: nil)
        playerItem?.addObserver(self, forKeyPath: "status", options: [.new, .initial], context: nil)
        isObserving = true
        print("[视频加载] 添加观察者成功")
        
        // 添加循环播放通知
        NotificationCenter.default.addObserver(self,
                                             selector: #selector(playerItemDidReachEnd),
                                             name: .AVPlayerItemDidPlayToEndTime,
                                             object: playerItem)
        
        // 强制立即检查状态并尝试启动播放
        if let playerItem = playerItem {
            // 立即测试播放器状态
            if playerItem.status == .readyToPlay {
                print("[视频加载] 状态已就绪，立即触发播放")
                self.isLoading = false
                self.onLoadingChange?(false)
                self.play()
            }
            
            // 同时安排延迟检查，确保即使初始检查失败也能开始播放
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                if playerItem.status == .readyToPlay {
                    print("[视频加载] 延迟检查：状态就绪，开始播放")
                    self.isLoading = false
                    self.onLoadingChange?(false)
                    self.play()
                } else if self.isLoading {
                    // 如果还在加载中，但播放器已经创建，尝试强制开始播放
                    print("[视频加载] 强制启动播放尝试")
                    self.isLoading = false
                    self.onLoadingChange?(false)
                    self.play()
                }
            }
        }
        
        return player!
    }
    
    // 播放控制
    func play() {
        isPlaying = true
        player?.play()
    }
    
    func pause() {
        isPlaying = false
        player?.pause()
    }
    
    // 循环播放
    @objc private func playerItemDidReachEnd() {
        player?.seek(to: .zero)
        if isPlaying {
            player?.play()
        }
    }
    
    // 观察加载状态
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        guard let playerItem = object as? AVPlayerItem else { return }
        
        if keyPath == "loadedTimeRanges" {
            // 检查预加载进度
            let loadedRanges = playerItem.loadedTimeRanges
            if let timeRange = loadedRanges.first?.timeRangeValue {
                let startTime = CMTimeGetSeconds(timeRange.start)
                let duration = CMTimeGetSeconds(timeRange.duration)
                let totalLoaded = startTime + duration
                
                print("[视频加载] 已缓冲: \(totalLoaded) 秒, 状态: \(playerItem.status.rawValue)")
                
                // 降低加载门槛，只需要加载0.1秒就开始播放，提高响应速度
                if totalLoaded >= 0.1 && playerItem.status == .readyToPlay && isLoading {
                    DispatchQueue.main.async { [weak self] in
                        guard let self = self else { return }
                        self.isLoading = false
                        self.onLoadingChange?(false)
                        self.play() // 自动开始播放
                        print("[视频加载] 成功: 视频已预加载 \(totalLoaded) 秒，开始播放")
                    }
                }
            } else {
                print("[视频加载] 警告: 无法获取缓冲区信息")
            }
        }
        
        if keyPath == "status" {
            print("[视频加载] 状态改变: \(playerItem.status.rawValue)")
            
            if playerItem.status == .readyToPlay {
                // 视频已就绪
                DispatchQueue.main.async { [weak self] in
                    guard let self = self else { return }
                    self.onReadyToPlay?()
                    print("[视频状态] 已就绪")
                    
                    // 如果状态就绪，立即开始播放
                    if self.isLoading {
                        self.isLoading = false
                        self.onLoadingChange?(false)
                        self.play()
                        print("[视频加载] 成功: 状态已就绪，开始播放")
                    }
                }
            } else if playerItem.status == .failed {
                let errorDesc = playerItem.error?.localizedDescription ?? "未知错误"
                print("[视频加载] 错误: 状态为失败，原因: \(errorDesc)")
                
                DispatchQueue.main.async { [weak self] in
                    guard let self = self else { return }
                    
                    // 尽管失败，仍然尝试播放
                    self.isLoading = false
                    self.onLoadingChange?(false)
                    self.play() // 尝试播放可能有助于恢复某些错误
                }
            } else if playerItem.status == .unknown {
                print("[视频状态] 未知状态")
                
                // 对于未知状态，等待短暂时间后强制开始播放
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                    guard let self = self, self.isLoading else { return }
                    print("[视频加载] 未知状态下强制开始播放")
                    self.isLoading = false
                    self.onLoadingChange?(false)
                    self.play()
                }
            }
        }
    }
    
    // 清理资源
    func cleanup() {
        print("[视频播放器] 清理资源")
        
        // 停止播放
        player?.pause()
        
        // 安全地移除观察者
        if isObserving {
            // 使用try-catch来避免移除观察者失败导致崩溃
            do {
                if let item = playerItem {
                    try? item.removeObserver(self, forKeyPath: "loadedTimeRanges")
                    try? item.removeObserver(self, forKeyPath: "status")
                }
            } catch {
                print("[视频播放器] 安全移除观察者失败: \(error)")
            }
            isObserving = false
        }
        
        // 移除通知
        NotificationCenter.default.removeObserver(self)
        
        // 释放资源
        player = nil
        playerItem = nil
        asset = nil
    }
    
    // 新增：配置音频会话
    private func configureAudioSession() {
        do {
            // 配置音频会话，确保声音可以通过扬声器播放
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback, mode: .default)
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
            print("[音频] 已配置音频会话为播放模式")
        } catch {
            print("[音频] 配置音频会话失败: \(error.localizedDescription)")
        }
    }
    
    // 新增：获取文件大小的辅助方法
    private func getFileSize(url: URL) -> Int64 {
        do {
            let fileAttributes = try FileManager.default.attributesOfItem(atPath: url.path)
            if let fileSize = fileAttributes[.size] as? Int64 {
                return fileSize
            }
        } catch {
            print("[文件系统] 获取文件大小失败: \(error.localizedDescription)")
        }
        return 0
    }
}

// 滑动选择助手类
class SlideSelectHelper: ObservableObject {
    @Published var lastDraggedIndex: Int? = nil
    @Published var initialDragState: Bool = false
    @Published var isDragging: Bool = false
    
    // 重置选择状态
    func reset() {
        lastDraggedIndex = nil
        isDragging = false
    }
    
    // 开始拖动选择
    func startDragging(at index: Int, selectedItems: inout Set<Int>) {
        isDragging = true
        initialDragState = !selectedItems.contains(index)
        lastDraggedIndex = index
        
        // 根据初始状态添加或删除
        if initialDragState {
            selectedItems.insert(index)
        } else {
            selectedItems.remove(index)
        }
    }
    
    // 处理滑动选择
    func handleDragAction(for index: Int, selectedItems: inout Set<Int>) {
        // 如果是首次触摸，记录初始状态
        if !isDragging {
            startDragging(at: index, selectedItems: &selectedItems)
            return
        }
        
        // 如果拖动到了新的索引位置
        if lastDraggedIndex != index {
            // 非首次触摸，根据初始状态进行相应操作
            if initialDragState {
                selectedItems.insert(index)
            } else {
                selectedItems.remove(index)
            }
            lastDraggedIndex = index
        }
    }
}

// 网格项视图，用于封装每个媒体项的显示和手势处理
struct MediaGridItemView: View {
    let item: MediaType
    let index: Int
    let isMultiSelectMode: Bool
    @Binding var selectedItems: Set<Int>
    @ObservedObject var slideSelector: SlideSelectHelper
    let gridItemSize: CGFloat // 新增：网格项大小参数
    let onTap: () -> Void
    let onDelete: (MediaType) -> Void
    
    // 每个项目的坐标存储
    @State private var itemFrame: CGRect = .zero
    // 异步缩略图加载状态
    @State private var asyncThumbnail: UIImage? = nil
    @State private var isLoadingThumbnail: Bool = false
    
    var body: some View {
        ZStack(alignment: .bottomTrailing) {
            // 图片缩略图（异步优化版本，保持原始长宽比）
            Group {
                if let thumbnail = asyncThumbnail {
                    Image(uiImage: thumbnail)
                        .resizable()
                        .aspectRatio(contentMode: .fit) // 保持原始长宽比
                        .frame(width: gridItemSize, height: gridItemSize) // 使用动态大小
                        .cornerRadius(8)
                        .transition(.opacity) // 添加显示过渡动画
                } else if isLoadingThumbnail {
                    // 缩略图加载中
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: gridItemSize, height: gridItemSize)
                        .cornerRadius(8)
                        .overlay(
                            ProgressView()
                                .scaleEffect(0.6)
                        )
                } else if let thumbnail = item.thumbnail {
                    // 同步获取的缩略图（备用）
                    Image(uiImage: thumbnail)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: gridItemSize, height: gridItemSize)
                        .cornerRadius(8)
                        .transition(.opacity)
                } else {
                    // 缩略图加载失败的占位符
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: gridItemSize, height: gridItemSize)
                        .cornerRadius(8)
                        .overlay(
                            Image(systemName: "photo")
                                .foregroundColor(.gray)
                                .scaleEffect(0.8)
                        )
                }
            }
            .onAppear {
                loadThumbnailAsync()
            }
            .onChange(of: gridItemSize) { _ in
                // 当网格大小改变时重新加载缩略图
                loadThumbnailAsync()
            }
                
                // 如果是视频，在缩略图内部显示时长
                if case .video = item, let duration = item.duration {
                    // 时长文字，根据网格项大小动态调整字体大小和位置
                    let fontSize = max(8, gridItemSize * 0.08) // 字体大小与网格项大小成比例
                    let bottomPadding = max(4, gridItemSize * 0.1) // 底部边距
                    let trailingPadding = max(4, gridItemSize * 0.08) // 右边距
                    
                    Text(duration < 1.0 ? "短视频" : String(format: "%.0f秒", duration))
                        .font(.system(size: fontSize))
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .shadow(color: .black, radius: 1, x: 0, y: 0) // 添加文字阴影提高可见度
                        .padding(.bottom, bottomPadding)
                        .padding(.trailing, trailingPadding)
                }
                
                // 在多选模式下显示选中状态图标
                if isMultiSelectMode {
                    let iconSize = max(16, gridItemSize * 0.2) // 图标大小与网格项大小成比例
                    let iconFontSize = max(8, iconSize * 0.5) // 复选标记字体大小
                    let iconPadding = max(4, gridItemSize * 0.05) // 图标边距
                    
                    ZStack {
                        Circle()
                            .fill(selectedItems.contains(index) ? Color.blue : Color.gray.opacity(0.3))
                            .frame(width: iconSize, height: iconSize)
                        
                        if selectedItems.contains(index) {
                            Image(systemName: "checkmark")
                                .font(.system(size: iconFontSize, weight: .bold))
                                .foregroundColor(.white)
                        }
                    }
                    .padding(iconPadding)
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topTrailing)
                }
            }
        .background(
            GeometryReader { geometry in
                Color.clear
                    .preference(key: ItemBoundsPreferenceKey.self,
                                value: [ItemBounds(id: index, bounds: geometry.frame(in: .named("gallery")))])
                    .onAppear {
                        // 保存项目的框架到本地状态
                        self.itemFrame = geometry.frame(in: .named("gallery"))
                    }
            }
        )
        .onTapGesture {
            if isMultiSelectMode {
                // 在多选模式下切换选择状态
                if selectedItems.contains(index) {
                    selectedItems.remove(index)
                } else {
                    selectedItems.insert(index)
                }
            } else {
                // 正常模式下预览媒体
                onTap()
            }
        }
        .contextMenu {
            Button(role: .destructive) {
                onDelete(item)
            } label: {
                Label("删除", systemImage: "trash")
            }
            
            Button {
                // 分享功能可以在这里扩展
                if case .photo(let image) = item {
                    // TODO: 实现照片分享
                } else if case .video(let url) = item {
                    // TODO: 实现视频分享
                }
            } label: {
                Label(NSLocalizedString("gallery_share", comment: ""), systemImage: "square.and.arrow.up")
            }
        }
        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
            Button(role: .destructive) {
                onDelete(item)
            } label: {
                Label("删除", systemImage: "trash")
            }
        }
    }
    
    // 异步加载缩略图
    private func loadThumbnailAsync() {
        // 避免重复加载
        guard asyncThumbnail == nil && !isLoadingThumbnail else { return }
        
        switch item {
        case .photo(let image):
            isLoadingThumbnail = true
            let targetSize = CGSize(width: gridItemSize * 2, height: gridItemSize * 2) // 2x分辨率
            
            ThumbnailCache.shared.preloadThumbnail(for: image, size: targetSize) { thumbnail in
                withAnimation(.easeInOut(duration: 0.2)) {
                    self.asyncThumbnail = thumbnail
                    self.isLoadingThumbnail = false
                }
            }
            
        case .video(let url):
            // 视频缩略图使用现有的缓存机制
            asyncThumbnail = AppMediaStorage.shared.getCachedVideoThumbnail(for: url)
            isLoadingThumbnail = false
            
            // 监听视频缩略图生成完成的通知
            NotificationCenter.default.addObserver(
                forName: NSNotification.Name("VideoThumbnailGenerated"),
                object: nil,
                queue: .main
            ) { notification in
                if let notificationURL = notification.userInfo?["url"] as? URL,
                   let thumbnail = notification.userInfo?["thumbnail"] as? UIImage,
                   notificationURL == url {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        self.asyncThumbnail = thumbnail
                    }
                }
            }
        }
    }
}

// 定义一个项目边界偏好键，用于收集网格项的位置信息
struct ItemBounds: Identifiable, Equatable {
    let id: Int
    let bounds: CGRect
    
    static func == (lhs: ItemBounds, rhs: ItemBounds) -> Bool {
        return lhs.id == rhs.id && lhs.bounds == rhs.bounds
    }
}

struct ItemBoundsPreferenceKey: PreferenceKey {
    static var defaultValue: [ItemBounds] = []
    
    static func reduce(value: inout [ItemBounds], nextValue: () -> [ItemBounds]) {
        value.append(contentsOf: nextValue())
    }
}

// 应用内相册视图
struct AppGalleryView: View {
    @Binding var isPresented: Bool
    @State private var mediaItems: [MediaType] = []
    @State private var selectedItem: MediaType? = nil
    @State private var showFullScreen = false
    @State private var isVideoLoading = false
    @State private var isImageLoading = false  // 添加图片加载状态

    @State private var dragOffset: CGFloat = 0  // 添加拖动偏移量跟踪
    // 多选相关状态
    @State private var isMultiSelectMode: Bool = false
    @State private var selectedItems: Set<Int> = []
    // 排序相关状态
    @State private var isAscendingOrder: Bool = false  // 默认降序（最新的在最上面）
    // 滑动选择相关状态 - 使用滑动选择助手
    @StateObject private var slideSelector = SlideSelectHelper()
    
    // 索引构建进度相关状态
    @State private var isIndexBuilding: Bool = false
    @State private var indexProgress: Double = 0.0
    @State private var indexProgressMessage: String = ""
    
    // 使用新的播放器包装器替代旧的播放器和观察器
    @StateObject private var videoWrapper = VideoPlayerWrapper()
    
    @State private var itemFrames: [Int: CGRect] = [:]
    @State private var dragPosition: CGPoint = .zero
    
    // 缩放相关状态
    @State private var zoomScale: CGFloat = 1.0
    @State private var lastZoomScale: CGFloat = 1.0
    @State private var gridItemMinSize: CGFloat = 80  // 最小缩略图尺寸
    @State private var gridItemMaxSize: CGFloat = 200 // 最大缩略图尺寸
    @State private var currentGridItemSize: CGFloat = 100 // 当前缩略图尺寸
    @State private var showZoomHint: Bool = true // 控制缩放提示的显示
    
    // 优化缓存相关状态
    @State private static var mediaDateCache: [String: Date] = [:] // 媒体项日期缓存
    @State private static var photoFileMapping: [String: String] = [:] // 照片文件映射 (图片哈希 -> 文件名)
    @State private static var exifBlacklist: Set<String> = [] // EXIF黑名单 - 记录确认没有EXIF时间信息的图片
    @State private static var disableEXIFReading: Bool = true // 完全禁用EXIF读取以提升性能
    
    // 在视图出现时加载应用内存储的媒体项（优化版本）
    private func loadAppMedia() {
        selectedItems.removeAll()
        
        // 显示加载状态
        print("[相册加载] ⚡ 开始使用索引快速加载媒体列表")
        
        // 调试目录状态
        AppMediaStorage.shared.debugDirectoryStatus()
        
        // 🔧 修复：检查索引是否需要更新
        let (totalFiles, _, _, isReady) = AppMediaStorage.shared.getIndexStatus()
        
        // 检查实际文件数量是否与索引匹配
        let actualImageCount = (try? FileManager.default.contentsOfDirectory(at: AppMediaStorage.shared.imagesDirectory, includingPropertiesForKeys: nil))?.count ?? 0
        let actualVideoCount = (try? FileManager.default.contentsOfDirectory(at: AppMediaStorage.shared.videosDirectory, includingPropertiesForKeys: nil))?.count ?? 0
        let actualTotalFiles = actualImageCount + actualVideoCount
        
        print("[相册加载] 📊 文件数量检查: 索引=\(totalFiles), 实际=\(actualTotalFiles)")
        
        // 如果文件数量不匹配或索引未就绪，强制重建索引
        if !isReady || totalFiles != actualTotalFiles || totalFiles == 0 {
            print("[相册加载] 🔄 索引需要更新，将重建索引")
            forceRefreshIndex()
            return
        }
        
        // 🔧 修复：提前设置进度回调，确保第一次就能显示进度
        AppMediaStorage.shared.setIndexProgressCallback { message, progress in
            DispatchQueue.main.async {
                self.isIndexBuilding = true
                self.indexProgressMessage = message
                self.indexProgress = progress
                
                // 当进度完成时，隐藏进度条
                if progress >= 1.0 {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        self.isIndexBuilding = false
                    }
                }
            }
        }
        
        // 使用新的索引优化的异步加载方法
        AppMediaStorage.shared.loadMediaFilesAsync { mediaItems in
            print("[相册加载] ✅ 接收到 \(mediaItems.count) 个媒体项")
            
            // 更新UI（此时已在主线程）
            self.mediaItems = mediaItems
            
            // 如果有媒体项，应用简单的排序
            if !mediaItems.isEmpty {
                print("[相册加载] 📋 媒体项已从索引加载完成")
                
                // ⚡ 性能优化：索引已按时间排序，只需要根据用户偏好调整顺序
                if self.isAscendingOrder {
                    self.mediaItems.reverse() // 如果用户要升序，就反转（因为索引默认降序）
                    print("[相册加载] 🔄 调整为升序排列")
                } else {
                    print("[相册加载] ✅ 保持降序排列（默认）")
                }
                
                // 预加载可见区域的缩略图
                self.preloadVisibleThumbnails()
                
                // 确保隐藏进度界面
                DispatchQueue.main.async {
                    self.isIndexBuilding = false
                }
            } else {
                print("[相册加载] ⚠️ 没有媒体项，可能正在构建索引...")
            }
        }
    }
    
    // 🔧 新增：强制刷新索引的方法
    private func forceRefreshIndex() {
        print("[相册刷新] 🔄 强制刷新索引...")
        
        // 立即显示进度界面
        DispatchQueue.main.async {
            self.isIndexBuilding = true
            self.indexProgressMessage = "正在检查媒体文件..."
            self.indexProgress = 0.1
        }
        
        // 设置进度回调
        AppMediaStorage.shared.setIndexProgressCallback { message, progress in
            DispatchQueue.main.async {
                self.indexProgressMessage = message
                self.indexProgress = progress
                
                if progress >= 1.0 {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        self.isIndexBuilding = false
                    }
                }
            }
        }
        
        // 强制重建索引
        AppMediaStorage.shared.forceRebuildIndex {
            // 重建完成后重新加载
            DispatchQueue.main.async {
                self.loadAppMedia()
            }
        }
    }
    
    // 构建照片文件映射缓存
    private func buildPhotoFileMapping() {
        let imagesDirectory = AppMediaStorage.shared.imagesDirectory
        
        do {
            let imageFiles = try FileManager.default.contentsOfDirectory(
                at: imagesDirectory, 
                includingPropertiesForKeys: [.creationDateKey, .fileSizeKey]
            )
            
            print("[缓存优化] 开始构建照片文件映射，共 \(imageFiles.count) 个文件")
            
            // 按创建时间排序文件列表，最新的在前面
            let sortedFiles = imageFiles.sorted { url1, url2 in
                do {
                    let attr1 = try FileManager.default.attributesOfItem(atPath: url1.path)
                    let attr2 = try FileManager.default.attributesOfItem(atPath: url2.path)
                    
                    let date1 = attr1[FileAttributeKey.creationDate] as? Date ?? Date.distantPast
                    let date2 = attr2[FileAttributeKey.creationDate] as? Date ?? Date.distantPast
                    
                    return date1 > date2
                } catch {
                    return false
                }
            }
            
            for imageURL in sortedFiles {
                let fileName = imageURL.lastPathComponent
                
                // 缓存文件的创建时间和文件大小
                do {
                    let attributes = try FileManager.default.attributesOfItem(atPath: imageURL.path)
                    if let creationDate = attributes[FileAttributeKey.creationDate] as? Date {
                        Self.mediaDateCache[fileName] = creationDate
                    }
                } catch {
                    print("[缓存优化] 无法获取文件属性: \(fileName)")
                }
            }
            
            print("[缓存优化] 文件映射构建完成，已缓存 \(Self.mediaDateCache.count) 个时间戳")
        } catch {
            print("[缓存优化] 构建文件映射失败: \(error.localizedDescription)")
        }
    }
    
    // 应用排序顺序（超快速版本，直接使用索引时间信息）
    private func applySortOrder() {
        print("[相册排序] ⚡ 开始超快速排序，当前有 \(mediaItems.count) 个媒体项，升序=\(isAscendingOrder)")
        
        guard !mediaItems.isEmpty else {
            print("[相册排序] 媒体项为空，跳过排序")
            return
        }
        
        // 🚀 性能优化：直接使用索引信息排序，完全避免文件系统访问
        mediaItems.sort { item1, item2 in
            let date1 = getMediaCreationDateFromIndex(item1)
            let date2 = getMediaCreationDateFromIndex(item2)
            
            if isAscendingOrder {
                return date1 < date2 // 升序：最早的在前
            } else {
                return date1 > date2 // 降序：最新的在前
            }
        }
        
        let orderDesc = isAscendingOrder ? "升序排列（最早在前）" : "降序排列（最新在前）"
        print("[相册排序] ✅ 完成超快速排序：\(orderDesc)")
        
        // 输出前3个项目的时间用于调试
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        for (i, item) in mediaItems.prefix(3).enumerated() {
            let date = getMediaCreationDateFromIndex(item)
            let type: String
            switch item {
            case .photo(_): type = "照片"
            case .video(_): type = "视频"
            }
            print("[排序结果] \(i+1). \(type) - \(formatter.string(from: date))")
        }
    }
    
    // ⚡ 超快速时间获取：直接从索引查询，完全避免文件系统访问
    private func getMediaCreationDateFromIndex(_ item: MediaType) -> Date {
        switch item {
        case .photo(let image):
            // 🔧 修复：改进照片时间匹配逻辑
            guard let cgImage = image.cgImage else { 
                print("[照片时间] 无法获取CGImage，使用当前时间")
                return Date() 
            }
            
            let imageWidth = cgImage.width
            let imageHeight = cgImage.height
            let imageSize = cgImage.width * cgImage.height
            
            print("[照片时间] 匹配照片: \(imageWidth)x\(imageHeight), 像素数: \(imageSize)")
            
            // 在索引中查找匹配的图片文件
            let imageIndexes = AppMediaStorage.shared.getMediaFileIndex().filter { $0.fileType == .image }
            
            // 🔧 改进匹配逻辑：优先精确尺寸匹配，然后是近似匹配
            // 1. 首先尝试精确尺寸匹配
            for indexItem in imageIndexes {
                // 根据文件大小推算图片尺寸
                let estimatedPixels = Int(indexItem.fileSize / 3) // 估算：每像素3字节
                let sizeDifference = abs(imageSize - estimatedPixels)
                let tolerance = imageSize / 10 // 允许10%误差
                
                if sizeDifference <= tolerance {
                    print("[照片时间] ✅ 找到匹配的图片索引: \(indexItem.fileName), 时间: \(indexItem.creationDate)")
                    return indexItem.creationDate
                }
            }
            
            // 2. 如果精确匹配失败，使用最新图片的时间（按修改时间排序）
            let sortedImageIndexes = imageIndexes.sorted { $0.lastModified > $1.lastModified }
            if let latestImage = sortedImageIndexes.first {
                print("[照片时间] ⚠️ 使用最新图片时间作为备选: \(latestImage.fileName), 时间: \(latestImage.creationDate)")
                return latestImage.creationDate
            }
            
            // 3. 最后备选方案
            print("[照片时间] ❌ 无法匹配，使用当前时间")
            return Date()
            
        case .video(let url):
            let fileName = url.lastPathComponent
            
            // 直接在索引中查找视频文件
            let videoIndexes = AppMediaStorage.shared.getMediaFileIndex().filter { $0.fileType == .video }
            if let matchedIndex = videoIndexes.first(where: { $0.fileName == fileName }) {
                print("[视频时间] ✅ 找到匹配的视频索引: \(fileName), 时间: \(matchedIndex.creationDate)")
                return matchedIndex.creationDate
            }
            
            // 如果索引中没找到，尝试从文件系统获取
            if let attributes = try? FileManager.default.attributesOfItem(atPath: url.path),
               let creationDate = attributes[FileAttributeKey.creationDate] as? Date {
                print("[视频时间] ⚠️ 从文件系统获取视频时间: \(fileName), 时间: \(creationDate)")
                return creationDate
            }
            
            // 最后备选：使用最新视频的时间
            let sortedVideoIndexes = videoIndexes.sorted { $0.creationDate > $1.creationDate }
            if let latestVideo = sortedVideoIndexes.first {
                print("[视频时间] ⚠️ 使用最新视频时间作为备选: \(latestVideo.fileName), 时间: \(latestVideo.creationDate)")
                return latestVideo.creationDate
            }
            
            print("[视频时间] ❌ 无法匹配视频时间，使用当前时间")
            return Date()
        }
    }
    
    // 缓存版本的时间获取（保留用于向后兼容，但不再在排序中使用）
    private func getMediaCreationDateCached(_ item: MediaType) -> Date {
        switch item {
        case .photo(let image):
            let imageKey = generatePhotoKey(for: image)
            if let cachedDate = Self.mediaDateCache[imageKey] {
                return cachedDate
            }
            
            // 尝试通过各种方法获取真实的照片时间
            let realDate = getMediaCreationDate(item)
            Self.mediaDateCache[imageKey] = realDate
            return realDate
            
        case .video(let url):
            let fileName = url.lastPathComponent
            if let cachedDate = Self.mediaDateCache[fileName] {
                return cachedDate
            }
            // 快速获取文件创建时间，如果失败使用当前时间
            let fileDate = (try? FileManager.default.attributesOfItem(atPath: url.path))?[.creationDate] as? Date ?? Date()
            Self.mediaDateCache[fileName] = fileDate
            return fileDate
        }
    }
    
    // 获取媒体项的创建日期（修复版本，使用真实时间）
    private func getMediaCreationDate(_ item: MediaType) -> Date {
        switch item {
        case .photo(let image):
            print("[照片时间] 开始获取照片创建时间")
            
            // 生成一个稳定的缓存键
            let imageKey = generatePhotoKey(for: image)
            
            // 首先检查缓存
            if let cachedDate = Self.mediaDateCache[imageKey] {
                print("[照片时间] ✅ 从缓存获取: \(cachedDate)")
                return cachedDate
            }
            
            // 方法1: 优先通过精确文件匹配获取创建时间（基于图片特征）
            if let matchedDate = findPhotoCreationDateByFeatures(for: image) {
                print("[照片时间] ✅ 从精确文件匹配获取: \(matchedDate)")
                Self.mediaDateCache[imageKey] = matchedDate // 缓存结果
                return matchedDate
            }
            
            // 方法2: 尝试通过一般文件匹配获取创建时间（优先级提升）
            if let matchedDate = findPhotoCreationDate(for: image, key: imageKey) {
                print("[照片时间] ✅ 从文件匹配获取: \(matchedDate)")
                Self.mediaDateCache[imageKey] = matchedDate // 缓存结果
                return matchedDate
            }
            
            // 方法3: 最后尝试从EXIF数据获取（降低优先级，减少性能影响）
            if !Self.disableEXIFReading, let exifDate = extractEXIFDate(from: image) {
                print("[照片时间] ✅ 从EXIF获取: \(exifDate)")
                Self.mediaDateCache[imageKey] = exifDate // 缓存结果
                return exifDate
            } else if Self.disableEXIFReading {
                print("[照片时间] ⚡ EXIF读取已禁用，跳过EXIF处理")
            }
            
            // 方法4: 使用最新的应用启动时间作为基准（更接近拍摄时间）
            let appLaunchTime = Date().timeIntervalSince1970 - 600 // 假设照片是在最近10分钟内拍摄的
            let imageHash = abs(image.hashValue)
            let offsetSeconds = TimeInterval(imageHash % 600) // 在过去10分钟内分布
            let stableDate = Date(timeIntervalSince1970: appLaunchTime - offsetSeconds)
            print("[照片时间] ⚠️ 使用基于启动时间的稳定时间: \(stableDate)")
            Self.mediaDateCache[imageKey] = stableDate // 缓存结果
            return stableDate
            
        case .video(let url):
            let fileName = url.lastPathComponent
            
            // 检查缓存
            if let cachedDate = Self.mediaDateCache[fileName] {
                print("[视频时间] ✅ 从缓存获取: \(cachedDate)")
                return cachedDate
            }
            
            // 获取文件创建时间
            do {
                let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
                if let creationDate = attributes[FileAttributeKey.creationDate] as? Date {
                    print("[视频时间] ✅ 从文件属性获取: \(creationDate)")
                    Self.mediaDateCache[fileName] = creationDate // 缓存结果
                    return creationDate
                }
            } catch {
                print("[视频时间] ❌ 获取文件创建日期失败: \(error.localizedDescription)")
            }
            
            // 使用当前时间作为默认值
            let currentDate = Date()
            Self.mediaDateCache[fileName] = currentDate
            print("[视频时间] ⚠️ 使用当前时间作为默认: \(currentDate)")
            return currentDate
        }
    }
    
    // 生成照片的稳定键
    private func generatePhotoKey(for image: UIImage) -> String {
        // 使用图片的像素数据生成更稳定的键
        guard let cgImage = image.cgImage else {
            return "photo_\(image.hashValue)"
        }
        
        let width = cgImage.width
        let height = cgImage.height
        let bitsPerComponent = cgImage.bitsPerComponent
        
        return "photo_\(width)x\(height)_\(bitsPerComponent)_\(image.hashValue)"
    }
    
    // 通过文件匹配查找照片创建时间（增强版本）
    private func findPhotoCreationDate(for image: UIImage, key: String) -> Date? {
        let imagesDirectory = AppMediaStorage.shared.imagesDirectory
        
        // 尝试基于图片特征快速匹配
        guard let cgImage = image.cgImage else { return nil }
        let imageWidth = cgImage.width
        let imageHeight = cgImage.height
        
        print("[照片匹配] 查找图片: \(imageWidth)x\(imageHeight)")
        
        // 获取所有图片文件并按修改时间排序
        do {
            let imageFiles = try FileManager.default.contentsOfDirectory(
                at: imagesDirectory,
                includingPropertiesForKeys: [.creationDateKey, .contentModificationDateKey, .fileSizeKey],
                options: []
            )
            
            // 按修改时间降序排列（最新的在前）
            let sortedFiles = imageFiles.sorted { url1, url2 in
                do {
                    let attr1 = try FileManager.default.attributesOfItem(atPath: url1.path)
                    let attr2 = try FileManager.default.attributesOfItem(atPath: url2.path)
                    
                    let date1 = attr1[FileAttributeKey.modificationDate] as? Date ?? Date.distantPast
                    let date2 = attr2[FileAttributeKey.modificationDate] as? Date ?? Date.distantPast
                    
                    return date1 > date2
                } catch {
                    return false
                }
            }
            
            // 在最近的10个文件中查找匹配
            for fileURL in sortedFiles.prefix(10) {
                do {
                    let attributes = try FileManager.default.attributesOfItem(atPath: fileURL.path)
                    let fileSize = attributes[FileAttributeKey.size] as? Int64 ?? 0
                    
                    // 估算目标图片的大小
                    let estimatedSize = Int64(imageWidth * imageHeight * 3) // RGB大约每像素3字节
                    
                    // 文件大小在合理范围内，可能是匹配的文件
                    if fileSize > estimatedSize / 4 && fileSize < estimatedSize * 4 {
                        // 获取创建时间，优先使用创建时间，其次是修改时间
                        var fileCreationDate: Date?
                        
                        if let creationDate = attributes[FileAttributeKey.creationDate] as? Date {
                            fileCreationDate = creationDate
                            print("[照片匹配] 找到匹配文件（创建时间）: \(fileURL.lastPathComponent), 时间: \(creationDate)")
                        } else if let modificationDate = attributes[FileAttributeKey.modificationDate] as? Date {
                            fileCreationDate = modificationDate
                            print("[照片匹配] 找到匹配文件（修改时间）: \(fileURL.lastPathComponent), 时间: \(modificationDate)")
                        }
                        
                        if let date = fileCreationDate {
                            // 缓存结果
                            Self.mediaDateCache[fileURL.lastPathComponent] = date
                            return date
                        }
                    }
                } catch {
                    print("[照片匹配] 读取文件属性失败: \(fileURL.lastPathComponent)")
                    continue
                }
            }
        } catch {
            print("[照片匹配] 读取目录失败: \(error.localizedDescription)")
        }
        
        return nil
    }
    
    // 基于图片特征的精确文件匹配
    private func findPhotoCreationDateByFeatures(for image: UIImage) -> Date? {
        let imagesDirectory = AppMediaStorage.shared.imagesDirectory
        
        guard let cgImage = image.cgImage else { return nil }
        let imageWidth = cgImage.width
        let imageHeight = cgImage.height
        let expectedFileSize = Int64(imageWidth * imageHeight * 4) // 估算文件大小（RGBA每像素4字节）
        
        print("[精确匹配] 查找图片: \(imageWidth)x\(imageHeight), 估算大小: \(expectedFileSize)")
        
        do {
            let imageFiles = try FileManager.default.contentsOfDirectory(
                at: imagesDirectory,
                includingPropertiesForKeys: [.creationDateKey, .contentModificationDateKey, .fileSizeKey],
                options: []
            )
            
            // 按修改时间降序排列，优先检查最新的文件
            let sortedFiles = imageFiles.sorted { url1, url2 in
                do {
                    let attr1 = try FileManager.default.attributesOfItem(atPath: url1.path)
                    let attr2 = try FileManager.default.attributesOfItem(atPath: url2.path)
                    
                    let date1 = attr1[FileAttributeKey.modificationDate] as? Date ?? Date.distantPast
                    let date2 = attr2[FileAttributeKey.modificationDate] as? Date ?? Date.distantPast
                    
                    return date1 > date2
                } catch {
                    return false
                }
            }
            
            // 在最近的5个文件中查找最佳匹配
            for fileURL in sortedFiles.prefix(5) {
                do {
                    let attributes = try FileManager.default.attributesOfItem(atPath: fileURL.path)
                    let fileSize = attributes[FileAttributeKey.size] as? Int64 ?? 0
                    
                    // 文件大小匹配度检查（允许50%的误差范围）
                    let sizeRatio = Double(fileSize) / Double(expectedFileSize)
                    if sizeRatio > 0.1 && sizeRatio < 10.0 {
                        // 尝试加载文件中的图片进行尺寸验证
                        if let fileImage = UIImage(contentsOfFile: fileURL.path),
                           let fileCGImage = fileImage.cgImage {
                            
                            // 尺寸精确匹配
                            if fileCGImage.width == imageWidth && fileCGImage.height == imageHeight {
                                print("[精确匹配] ✅ 找到尺寸精确匹配: \(fileURL.lastPathComponent)")
                                
                                // 返回创建时间，优先使用创建时间
                                if let creationDate = attributes[FileAttributeKey.creationDate] as? Date {
                                    return creationDate
                                } else if let modificationDate = attributes[FileAttributeKey.modificationDate] as? Date {
                                    return modificationDate
                                }
                            }
                        }
                    }
                } catch {
                    print("[精确匹配] 读取文件失败: \(fileURL.lastPathComponent)")
                    continue
                }
            }
        } catch {
            print("[精确匹配] 读取目录失败: \(error.localizedDescription)")
        }
        
        return nil
    }
    
    // 查找最近的照片文件时间戳
    private func findMostRecentPhotoFile() -> Date? {
        let imagesDirectory = AppMediaStorage.shared.imagesDirectory
        
        do {
            let imageFiles = try FileManager.default.contentsOfDirectory(
                at: imagesDirectory,
                includingPropertiesForKeys: [.creationDateKey, .contentModificationDateKey],
                options: []
            )
            
            // 找到最新的文件时间戳
            var mostRecentDate: Date?
            
            for fileURL in imageFiles {
                do {
                    let attributes = try FileManager.default.attributesOfItem(atPath: fileURL.path)
                    
                    // 优先使用创建时间，其次使用修改时间
                    var fileDate: Date?
                    if let creationDate = attributes[FileAttributeKey.creationDate] as? Date {
                        fileDate = creationDate
                    } else if let modificationDate = attributes[FileAttributeKey.modificationDate] as? Date {
                        fileDate = modificationDate
                    }
                    
                    if let date = fileDate {
                        if mostRecentDate == nil || date > mostRecentDate! {
                            mostRecentDate = date
                        }
                    }
                } catch {
                    continue
                }
            }
            
            return mostRecentDate
        } catch {
            print("[照片时间] 查找最近文件失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    // 从EXIF数据提取日期（优化版本 - 使用黑名单避免重复无效尝试）
    private func extractEXIFDate(from image: UIImage) -> Date? {
        // 生成图片的唯一标识符
        let imageKey = generatePhotoKey(for: image)
        
        // 检查EXIF黑名单，如果已确认没有EXIF信息，直接跳过
        if Self.exifBlacklist.contains(imageKey) {
            print("[EXIF] ⚡ 跳过已知无EXIF信息的图片: \(imageKey)")
            return nil
        }
        
        // 尝试不同的图片数据获取方式
        var imageData: Data?
        
        // 方法1: 尝试PNG格式（保留更多元数据）
        if let pngData = image.pngData() {
            imageData = pngData
            print("[EXIF] 使用PNG数据")
        }
        // 方法2: 尝试高质量JPEG格式
        else if let jpegData = image.jpegData(compressionQuality: 1.0) {
            imageData = jpegData
            print("[EXIF] 使用JPEG数据")
        }
        // 方法3: 尝试原始图片数据（如果是从文件加载的）
        else if let cgImage = image.cgImage {
            print("[EXIF] 图片无法转换为数据格式，可能是内存中的图片")
            // 直接加入黑名单，避免后续尝试
            Self.exifBlacklist.insert(imageKey)
            return nil
        }
        
        guard let data = imageData,
              let imageSource = CGImageSourceCreateWithData(data as CFData, nil),
              let properties = CGImageSourceCopyPropertiesAtIndex(imageSource, 0, nil) as? [String: Any] else {
            print("[EXIF] 无法获取图片源或属性")
            // 加入黑名单，避免后续尝试
            Self.exifBlacklist.insert(imageKey)
            return nil
        }
        
        print("[EXIF] 图片属性键: \(properties.keys)")
        
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy:MM:dd HH:mm:ss"
        formatter.timeZone = TimeZone.current
        
        // 尝试多种EXIF日期字段
        // 1. EXIF DateTimeOriginal (最精确的拍摄时间)
        if let exifDict = properties[kCGImagePropertyExifDictionary as String] as? [String: Any] {
            print("[EXIF] EXIF字典键: \(exifDict.keys)")
            
            if let dateTimeString = exifDict[kCGImagePropertyExifDateTimeOriginal as String] as? String {
                print("[EXIF] 找到DateTimeOriginal: \(dateTimeString)")
                if let exifDate = formatter.date(from: dateTimeString) {
                    print("[EXIF] 成功解析DateTimeOriginal: \(exifDate)")
                    return exifDate
                }
            }
            
            // 2. EXIF DateTime (文件修改时间)
            if let dateTimeString = exifDict["DateTime"] as? String {
                print("[EXIF] 找到DateTime: \(dateTimeString)")
                if let exifDate = formatter.date(from: dateTimeString) {
                    print("[EXIF] 成功解析DateTime: \(exifDate)")
                    return exifDate
                }
            }
        }
        
        // 3. TIFF DateTime
        if let tiffDict = properties[kCGImagePropertyTIFFDictionary as String] as? [String: Any] {
            print("[EXIF] TIFF字典键: \(tiffDict.keys)")
            
            if let dateTimeString = tiffDict[kCGImagePropertyTIFFDateTime as String] as? String {
                print("[EXIF] 找到TIFF DateTime: \(dateTimeString)")
                if let tiffDate = formatter.date(from: dateTimeString) {
                    print("[EXIF] 成功解析TIFF DateTime: \(tiffDate)")
                    return tiffDate
                }
            }
        }
        
        // 4. GPS时间戳
        if let gpsDict = properties[kCGImagePropertyGPSDictionary as String] as? [String: Any] {
            print("[EXIF] GPS字典键: \(gpsDict.keys)")
            
            if let dateStamp = gpsDict[kCGImagePropertyGPSDateStamp as String] as? String {
                print("[EXIF] 找到GPS日期: \(dateStamp)")
                
                // GPS时间戳通常只有日期，没有具体时间，所以用日期的开始时间
                let gpsFormatter = DateFormatter()
                gpsFormatter.dateFormat = "yyyy:MM:dd"
                gpsFormatter.timeZone = TimeZone(identifier: "UTC")
                
                if let gpsDate = gpsFormatter.date(from: dateStamp) {
                    print("[EXIF] 成功解析GPS日期: \(gpsDate)")
                    return gpsDate
                }
            }
        }
        
        print("[EXIF] 未找到任何可用的时间信息")
        // 将此图片加入黑名单，避免后续重复尝试
        Self.exifBlacklist.insert(imageKey)
        print("[EXIF] ➕ 已将图片加入EXIF黑名单: \(imageKey)")
        return nil
    }
    
    // 性能优化方法：控制EXIF读取
    static func setEXIFReadingEnabled(_ enabled: Bool) {
        disableEXIFReading = !enabled
        if !enabled {
            print("[性能优化] 已禁用EXIF读取，相册加载速度将显著提升")
        } else {
            print("[性能优化] 已启用EXIF读取，将尝试获取照片拍摄时间")
        }
    }
    
    // 清理EXIF黑名单缓存
    static func clearEXIFBlacklist() {
        exifBlacklist.removeAll()
        print("[性能优化] 已清理EXIF黑名单缓存")
    }

    
    // 切换排序顺序（超快速版本）
    private func toggleSortOrder() {
        print("[排序切换] ⚡ 开始超快速切换排序：当前升序=\(isAscendingOrder) -> \(!isAscendingOrder)")
        
        withAnimation(.easeInOut(duration: 0.3)) {
            isAscendingOrder.toggle()
            // 🚀 性能优化：直接反转数组，无需重新排序
            mediaItems.reverse()
            print("[排序切换] ✅ 完成瞬时排序切换，新状态：升序=\(isAscendingOrder)")
        }
        
        // 不再需要后台详细排序，因为索引已经是正确的时间顺序
    }
    
    // 全选功能
    private func selectAllItems() {
        for i in 0..<mediaItems.count {
            selectedItems.insert(i)
        }
    }
    
    // 取消全选功能
    private func deselectAllItems() {
        selectedItems.removeAll()
    }
    
    // 切换全选功能
    private func toggleSelectAll() {
        if selectedItems.count == mediaItems.count {
            deselectAllItems()
        } else {
            selectAllItems()
        }
    }
    
    // 新增：预加载选定媒体
    private func preloadSelectedMedia() {
        guard let item = selectedItem else { return }
        
        switch item {
        case .photo(let image):
            // 设置加载状态
            isImageLoading = true
            
            // 使用后台线程预处理图片，确保在显示前已完全加载
            DispatchQueue.global(qos: .userInitiated).async {
                // 强制解码图片，确保UI线程不会因为解码而卡顿
                let cgImage = image.cgImage
                let preloadedImage = UIImage(cgImage: cgImage!)
                
                DispatchQueue.main.async {
                    // 更新UI状态
                    isImageLoading = false
                    // 如果需要，可以用预加载的图像替换原始图像
                }
            }
            
        case .video(let url):
            // 视频预加载在VideoWrapper中已实现
            isVideoLoading = true
            print("[相册] 预加载视频: \(url.lastPathComponent)")
        }
    }
    
    // 处理多选删除
    private func deleteSelectedItems() {
        // 将选中索引转换为对应的媒体项
        let itemsToDelete = selectedItems.compactMap { index -> MediaType? in
            guard index >= 0 && index < mediaItems.count else { return nil }
            return mediaItems[index]
        }
        
        // 直接执行批量删除，系统会提供确认
        performBatchDelete(items: itemsToDelete)
    }
    
    // 执行批量删除
    private func performBatchDelete(items: [MediaType]) {
        print("🗑️ 开始执行批量删除，共 \(items.count) 项")
        
        // 首先退出全屏模式（如果需要）
        if showFullScreen {
            showFullScreen = false
        }
        
        // 🔧 修复：使用改进的匹配逻辑获取要删除的项目索引
        var itemIndexes: [Int] = []
        for item in items {
            if let index = findMediaItemIndex(for: item) {
                itemIndexes.append(index)
            }
        }
        
        // 按降序排序索引，从后往前删除
        itemIndexes.sort(by: >)
        
        // 立即从UI中移除所有选中的项目
        withAnimation(.easeOut(duration: 0.4)) {
            for index in itemIndexes {
                if index < mediaItems.count {
                    mediaItems.remove(at: index)
                }
            }
        }
        
        print("✅ 立即从UI中移除 \(itemIndexes.count) 个项目")
        
        // 退出多选模式
        isMultiSelectMode = false
        selectedItems.removeAll()
        
        // 在后台执行实际删除操作
        let deleteGroup = DispatchGroup()
        var deletedCount = 0
        var failedItems: [(item: MediaType, index: Int)] = []
        
        for (i, item) in items.enumerated() {
            deleteGroup.enter()
            
            DispatchQueue.global(qos: .utility).async {
                AppMediaStorage.shared.deleteMedia(item) { success in
                    if success {
                        deletedCount += 1
                    } else {
                        // 记录失败的项目和其原始索引
                        if i < itemIndexes.count {
                            failedItems.append((item: item, index: itemIndexes[i]))
                        }
                    }
                    deleteGroup.leave()
                }
            }
        }
        
        // 所有删除操作完成后处理结果
        deleteGroup.notify(queue: .main) {
            print("📊 批量删除完成: 成功删除 \(deletedCount)/\(items.count) 项")
            
            if !failedItems.isEmpty {
                print("⚠️ 有 \(failedItems.count) 个项目删除失败，恢复UI状态")
                
                // 恢复删除失败的项目到UI中
                withAnimation(.easeIn(duration: 0.3)) {
                    for failedItem in failedItems.reversed() { // 反向插入以保持原有顺序
                        let insertIndex = min(failedItem.index, self.mediaItems.count)
                        self.mediaItems.insert(failedItem.item, at: insertIndex)
                    }
                }
                
                // 可以在这里显示错误提示
                print("💡 提示：\(failedItems.count) 个项目删除失败，已恢复到列表中")
            }
            
            // 🔧 修复：批量删除完成后立即刷新相册视图确保数据一致性
            if deletedCount > 0 {
                print("🔄 批量删除完成，立即刷新相册视图")
                self.refreshGalleryAfterDelete()
            }
        }
    }
    
    // 执行单个删除
    private func performSingleDelete(item: MediaType) {
        print("🗑️ 开始执行单个删除操作")
        
        // 如果在全屏模式且删除的是当前显示的项目，退出全屏
        if showFullScreen, selectedItem == item {
            showFullScreen = false
        }
        
        // 🔧 修复：改进项目匹配逻辑
        let itemIndex = findMediaItemIndex(for: item)
        
        if let index = itemIndex {
            // 立即从UI中移除
            withAnimation(.easeOut(duration: 0.3)) {
                mediaItems.remove(at: index)
            }
            print("✅ 立即从UI中移除项目，索引: \(index)")
        } else {
            print("⚠️ 未找到要删除的项目在列表中的位置")
        }
        
        // 更新选中状态
        if selectedItem == item {
            selectedItem = nil
        }
        
        // 在后台执行实际删除操作
        AppMediaStorage.shared.deleteMedia(item) { success in
            DispatchQueue.main.async {
                if success {
                    print("✅ 媒体删除成功，立即刷新相册视图")
                    
                    // 🔧 修复：删除成功后立即刷新相册视图确保数据一致性
                    self.refreshGalleryAfterDelete()
                    
                } else {
                    print("❌ 媒体删除失败，恢复UI状态")
                    
                    // 如果删除失败，恢复UI状态
                    if let index = itemIndex {
                        withAnimation(.easeIn(duration: 0.3)) {
                            self.mediaItems.insert(item, at: min(index, self.mediaItems.count))
                        }
                    }
                    
                    // 显示错误提示（可以在这里添加Alert或Toast）
                    print("⚠️ 删除操作失败，已恢复界面状态")
                }
            }
        }
    }
    
    // 🔧 新增：改进的媒体项索引查找方法
    private func findMediaItemIndex(for targetItem: MediaType) -> Int? {
        switch targetItem {
        case .photo(let targetImage):
            print("🔍 开始精确查找照片索引...")
            
            // 策略1: 使用对象引用匹配（最精确）
            for (index, item) in mediaItems.enumerated() {
                if case .photo(let image) = item, image === targetImage {
                    print("✅ 通过对象引用找到匹配的照片，索引: \(index)")
                    return index
                }
            }
            
            // 策略2: 使用图片内容哈希匹配
            if let targetHash = generateImageContentHash(targetImage) {
                for (index, item) in mediaItems.enumerated() {
                    if case .photo(let image) = item,
                       let imageHash = generateImageContentHash(image),
                       imageHash == targetHash {
                        print("✅ 通过内容哈希找到匹配的照片，索引: \(index)")
                        return index
                    }
                }
            }
            
            // 策略3: 使用尺寸和文件大小匹配
            guard let targetCGImage = targetImage.cgImage else { return nil }
            let targetWidth = targetCGImage.width
            let targetHeight = targetCGImage.height
            let targetDataSize = targetImage.jpegData(compressionQuality: 0.9)?.count ?? 0
            
            print("🎯 目标照片特征: \(targetWidth)x\(targetHeight), 数据大小: \(targetDataSize) bytes")
            
            var bestMatchIndex: Int?
            var bestMatchScore: Double = 0
            
            for (index, item) in mediaItems.enumerated() {
                if case .photo(let image) = item,
                   let cgImage = image.cgImage {
                    
                    let imageWidth = cgImage.width
                    let imageHeight = cgImage.height
                    let imageDataSize = image.jpegData(compressionQuality: 0.9)?.count ?? 0
                    
                    // 计算匹配度分数
                    var matchScore: Double = 0
                    
                    // 尺寸匹配加分
                    if imageWidth == targetWidth && imageHeight == targetHeight {
                        matchScore += 40 // 尺寸精确匹配
                    } else {
                        let sizeRatio = Double(min(imageWidth, targetWidth)) / Double(max(imageWidth, targetWidth))
                        matchScore += sizeRatio * 20 // 尺寸相似度
                    }
                    
                    // 数据大小匹配加分
                    if targetDataSize > 0 && imageDataSize > 0 {
                        let sizeDiff = abs(imageDataSize - targetDataSize)
                        let tolerance = max(targetDataSize / 10, 1000)
                        if sizeDiff <= tolerance {
                            matchScore += 30 // 文件大小相近
                        }
                    }
                    
                    // 像素数匹配加分
                    let targetPixels = targetWidth * targetHeight
                    let imagePixels = imageWidth * imageHeight
                    if targetPixels == imagePixels {
                        matchScore += 30 // 像素数相同
                    }
                    
                    print("📊 索引\(index)匹配分数: \(matchScore), 尺寸: \(imageWidth)x\(imageHeight)")
                    
                    // 如果匹配度足够高且比之前的匹配更好
                    if matchScore > 80 && matchScore > bestMatchScore {
                        bestMatchScore = matchScore
                        bestMatchIndex = index
                    }
                }
            }
            
            if let index = bestMatchIndex {
                print("✅ 通过综合匹配找到照片，索引: \(index), 匹配度: \(bestMatchScore)")
                return index
            }
            
            print("❌ 未找到匹配的照片")
            return nil
            
        case .video(let targetURL):
            // 对于视频，使用文件路径匹配
            for (index, item) in mediaItems.enumerated() {
                if case .video(let url) = item, url.path == targetURL.path {
                    print("✅ 找到匹配的视频，索引: \(index), 文件: \(targetURL.lastPathComponent)")
                    return index
                }
            }
            
            print("❌ 未找到匹配的视频")
            return nil
        }
    }
    
    // 🔧 新增：生成图片内容哈希的方法
    private func generateImageContentHash(_ image: UIImage) -> String? {
        guard let cgImage = image.cgImage else { return nil }
        
        // 创建一个小尺寸的图片进行哈希计算，提高性能
        let targetSize = CGSize(width: 8, height: 8)
        
        UIGraphicsBeginImageContextWithOptions(targetSize, true, 1.0)
        defer { UIGraphicsEndImageContext() }
        
        guard let context = UIGraphicsGetCurrentContext() else { return nil }
        
        // 绘制缩放后的图片
        context.interpolationQuality = .none // 使用最简单的插值以保持特征
        context.draw(cgImage, in: CGRect(origin: .zero, size: targetSize))
        
        guard let smallCGImage = UIGraphicsGetImageFromCurrentImageContext()?.cgImage else { return nil }
        
        // 获取像素数据
        let width = smallCGImage.width
        let height = smallCGImage.height
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let totalBytes = height * bytesPerRow
        
        var pixelData = [UInt8](repeating: 0, count: totalBytes)
        
        guard let pixelContext = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: bytesPerRow,
            space: CGColorSpaceCreateDeviceRGB(),
            bitmapInfo: CGImageAlphaInfo.noneSkipLast.rawValue
        ) else { return nil }
        
        pixelContext.draw(smallCGImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        
        // 计算简单的哈希值
        var hash: UInt64 = 0
        for i in stride(from: 0, to: totalBytes, by: bytesPerPixel) {
            // 只使用RGB值，忽略Alpha
            let r = UInt64(pixelData[i])
            let g = UInt64(pixelData[i + 1])
            let b = UInt64(pixelData[i + 2])
            
            // 创建一个简单的哈希
            hash = hash &* 31 &+ r &* 65537 &+ g &* 257 &+ b
        }
        
        return String(hash)
    }
    
    // 缩放相关方法
    private func handleZoomGesture(value: MagnificationGesture.Value) {
        // 第一次缩放时隐藏提示
        if showZoomHint {
            showZoomHint = false
        }
        
        // 计算新的缩放比例
        let newScale = lastZoomScale * value
        zoomScale = max(0.5, min(newScale, 3.0)) // 限制缩放范围在0.5x到3.0x之间
        
        // 根据缩放比例计算网格项大小
        let newSize = 100 * zoomScale // 基础大小100，根据缩放调整
        currentGridItemSize = max(gridItemMinSize, min(newSize, gridItemMaxSize))
    }
    
    private func handleZoomEnded() {
        // 缩放结束时保存当前缩放比例
        lastZoomScale = zoomScale
        
        // 添加一些阻尼效果，使缩放更自然
        withAnimation(.easeOut(duration: 0.2)) {
            // 可以在这里添加一些缩放结束后的微调
        }
    }
    
    // 预加载可见区域的缩略图
    private func preloadVisibleThumbnails() {
        let visibleCount = min(20, mediaItems.count) // 预加载前20个项目
        let targetSize = CGSize(width: currentGridItemSize * 2, height: currentGridItemSize * 2)
        
        DispatchQueue.global(qos: .userInitiated).async {
            for i in 0..<visibleCount {
                let item = self.mediaItems[i]
                
                switch item {
                case .photo(let image):
                    // 异步预加载照片缩略图
                    ThumbnailCache.shared.preloadThumbnail(for: image, size: targetSize) { _ in
                        // 预加载完成，不需要UI更新
                    }
                    
                case .video(_):
                    // 视频缩略图已由 AppMediaStorage 处理
                    break
                }
            }
            
            print("[缩略图预加载] 已开始预加载前 \(visibleCount) 个缩略图")
        }
    }
    
    // 缓存清理方法
    private func cleanupCache() {
        print("[缓存优化] 收到内存警告，开始清理缓存")
        
        // 清理日期缓存，只保留最近的50个条目
        if Self.mediaDateCache.count > 50 {
            let sortedEntries = Self.mediaDateCache.sorted { $0.value > $1.value }
            Self.mediaDateCache = Dictionary(Array(sortedEntries.prefix(50)), uniquingKeysWith: { first, _ in first })
            print("[缓存优化] 日期缓存已清理，保留 \(Self.mediaDateCache.count) 个条目")
        }
        
        // 清理缩略图缓存
        ThumbnailCache.shared.clearAllThumbnails()
        
        // 通知 AppMediaStorage 清理其缓存
        DispatchQueue.global(qos: .utility).async {
            // 这里可以添加对 AppMediaStorage 缓存的清理调用
            print("[缓存优化] 缓存清理完成")
        }
    }
    
    // 🔧 新增：删除后刷新相册的专用方法
    private func refreshGalleryAfterDelete() {
        print("[相册刷新] 🔄 开始删除后刷新...")
        
        // 🔧 改进：先同步索引，再刷新界面，确保数据完全一致
        AppMediaStorage.shared.syncIndexAfterDeletion {
            // 索引同步完成后，重新加载媒体数据
            AppMediaStorage.shared.loadMediaFilesAsync { refreshedItems in
                DispatchQueue.main.async {
                    let previousCount = self.mediaItems.count
                    
                    withAnimation(.easeInOut(duration: 0.5)) {
                        self.mediaItems = refreshedItems
                        
                        // 应用当前的排序偏好
                        if self.isAscendingOrder {
                            self.mediaItems.reverse()
                        }
                    }
                    
                    let newCount = self.mediaItems.count
                    print("✅ 删除后刷新完成: \(previousCount) -> \(newCount) 项")
                    
                    // 预加载新的缩略图
                    self.preloadVisibleThumbnails()
                    
                    // 清理选择状态
                    self.selectedItems.removeAll()
                }
            }
        }
    }
    
    var body: some View {
        ZStack {
            // 导航视图使用背景色包裹，确保下滑手势在整个屏幕范围内有效
            Color.black.opacity(0.01)
                .edgesIgnoringSafeArea(.all)
                .contentShape(Rectangle()) // 确保整个区域接收手势
                .gesture(
                    DragGesture()
                        .onChanged { gesture in
                            // 只响应向下滑动
                            if gesture.translation.height > 0 {
                                self.dragOffset = gesture.translation.height
                            }
                        }
                        .onEnded { gesture in
                            // 如果下滑超过50点，关闭相册（降低阈值使关闭更容易）
                            if gesture.translation.height > 50 {
                                withAnimation {
                                    self.isPresented = false
                                }
                            } else {
                                // 否则回弹
                                withAnimation {
                                    self.dragOffset = 0
                                }
                            }
                        }
                )
            
            NavigationView {
                ZStack {
                    // 主内容
                    VStack {
                        if mediaItems.isEmpty && !isIndexBuilding {
                            VStack {
                                Image(systemName: "photo.on.rectangle")
                                    .font(.system(size: 60))
                                    .foregroundColor(.gray)
                                    .padding()
                                
                                Text(NSLocalizedString("gallery_empty", comment: ""))
                                    .font(.headline)
                                    .foregroundColor(.gray)
                            }
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                        } else if isIndexBuilding {
                            // 使用专用的索引进度视图
                            IndexProgressView(
                                progress: indexProgress,
                                message: indexProgressMessage
                            )
                            .transition(.opacity.combined(with: .scale(scale: 0.9)))
                        } else {
                            ScrollView {
                                LazyVGrid(columns: [GridItem(.adaptive(minimum: currentGridItemSize, maximum: currentGridItemSize * 1.5))], spacing: 8) {
                                    ForEach(Array(mediaItems.enumerated()), id: \.offset) { index, item in
                                        MediaGridItemView(
                                            item: item,
                                            index: index,
                                            isMultiSelectMode: isMultiSelectMode,
                                            selectedItems: $selectedItems,
                                            slideSelector: slideSelector,
                                            gridItemSize: currentGridItemSize, // 传递当前网格项大小
                                            onTap: {
                                                selectedItem = item
                                                showFullScreen = true
                                            },
                                            onDelete: { item in
                                                performSingleDelete(item: item)
                                            }
                                        )
                                    }
                                }
                                .padding()
                                .animation(.easeInOut(duration: 0.1), value: currentGridItemSize) // 添加大小变化动画
                            }
                            .coordinateSpace(name: "gallery")
                            // 添加缩放手势（优先级较高，在非多选模式下生效）
                            .gesture(
                                !isMultiSelectMode ? 
                                MagnificationGesture()
                                    .onChanged { value in
                                        handleZoomGesture(value: value)
                                    }
                                    .onEnded { _ in
                                        handleZoomEnded()
                                    } : nil
                            )
                            // 添加全局拖动手势来实现连续选择（仅在多选模式下生效）
                            .gesture(
                                isMultiSelectMode ? 
                                DragGesture(minimumDistance: 5)
                                    .onChanged { value in
                                        dragPosition = value.location
                                        
                                        // 检查手指下方是否有项目
                                        for (index, frame) in itemFrames {
                                            if frame.contains(value.location) {
                                                slideSelector.handleDragAction(for: index, selectedItems: &selectedItems)
                                            }
                                        }
                                    }
                                    .onEnded { _ in
                                        // 拖动结束，重置拖动状态
                                        slideSelector.isDragging = false
                                    } : nil
                            )
                            .onPreferenceChange(ItemBoundsPreferenceKey.self) { preferences in
                                // 更新所有项目的坐标信息
                                for preference in preferences {
                                    itemFrames[preference.id] = preference.bounds
                                }
                            }
                        }
                    }
                }
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        if isMultiSelectMode {
                            Button(NSLocalizedString("gallery_cancel", comment: "")) {
                                isMultiSelectMode = false
                                selectedItems.removeAll()
                            }
                            .foregroundColor(.primary)
                        } else {
                            // 恢复关闭按钮
                            Button(NSLocalizedString("gallery_close", comment: "")) {
                                isPresented = false
                            }
                            .foregroundColor(.primary)
                            .font(.headline)
                        }
                    }
                    
                    // 导航栏标题
                    ToolbarItem(placement: .principal) {
                        Text(NSLocalizedString("gallery_album", comment: ""))
                            .font(.headline)
                    }
                    
                    ToolbarItem(placement: .navigationBarTrailing) {
                        HStack(spacing: 8) {
                            // 缩放重置按钮 - 只在非多选模式下显示，且只在非默认缩放时显示
                            if !isMultiSelectMode && abs(zoomScale - 1.0) > 0.1 {
                                Button(action: {
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        zoomScale = 1.0
                                        lastZoomScale = 1.0
                                        currentGridItemSize = 100 // 重置到默认大小
                                    }
                                }) {
                                    Image(systemName: "1.magnifyingglass")
                                        .font(.footnote)
                                }
                            }
                            
                            // 🔧 新增：刷新索引按钮（调试用）
                            if !isMultiSelectMode && !mediaItems.isEmpty {
                                Button(action: {
                                    forceRefreshIndex()
                                }) {
                                    Image(systemName: "arrow.clockwise")
                                        .font(.footnote)
                                }
                            }
                            
                            // 多选按钮
                            Button(action: { 
                                isMultiSelectMode.toggle()
                                selectedItems.removeAll()
                                slideSelector.reset()
                            }) {
                                Text(isMultiSelectMode ? NSLocalizedString("gallery_done", comment: "") : NSLocalizedString("gallery_select", comment: ""))
                                    .foregroundColor(isMultiSelectMode ? .blue : .primary)
                            }
                        }
                    }
                }
                // 移除NavigationView的下滑手势，已移到外层ZStack
                .onAppear {
                    loadAppMedia()
                    // 重置偏移量
                    dragOffset = 0
                    
                    // 5秒后自动隐藏缩放提示
                    DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                        withAnimation(.easeOut(duration: 0.5)) {
                            showZoomHint = false
                        }
                    }
                    
                    // 设置内存警告监听，清理缓存
                    NotificationCenter.default.addObserver(forName: UIApplication.didReceiveMemoryWarningNotification, object: nil, queue: .main) { _ in
                        cleanupCache()
                    }
                }
                .onDisappear {
                    // 移除通知监听
                    NotificationCenter.default.removeObserver(self, name: UIApplication.didReceiveMemoryWarningNotification, object: nil)
                }
                .onChange(of: selectedItem) { _ in
                    // 当选中项改变时预加载媒体
                    if selectedItem != nil {
                        preloadSelectedMedia()
                    }
                }
                .onChange(of: isMultiSelectMode) { newValue in
                    if !newValue {
                        // 退出多选模式时重置滑动选择状态
                        slideSelector.reset()
                        selectedItems.removeAll()
                    }
                }
                .overlay(
                    // 在相册底部显示缩放提示（仅在非多选模式且有媒体项时显示）
                    Group {
                        if !isMultiSelectMode && !mediaItems.isEmpty && showZoomHint {
                            VStack {
                                Spacer()
                                HStack {
                                    Spacer()
                                    Text("双指缩放调整缩略图大小")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(Color(.systemBackground).opacity(0.8))
                                        .cornerRadius(15)
                                        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                                    Spacer()
                                }
                                .padding(.bottom, 20)
                            }
                            .transition(.opacity)
                        }
                    }
                )
            }
            
            // 应用根据拖动偏移量的视觉效果
            .offset(y: dragOffset / 5)  // 减小偏移量以使动画更平滑
            // 根据拖动量应用缩放效果
            .scaleEffect(1.0 - min(dragOffset, 500) / 5000)  // 轻微的缩放效果
            
            // 添加右下角浮动按钮区域，仅在多选模式下显示
            if isMultiSelectMode {
                VStack(spacing: 12) {
                    // 全选/取消全选按钮
                    Button(action: {
                        toggleSelectAll()
                    }) {
                        ZStack {
                            Circle()
                                .fill(Color(.systemBackground))
                                .shadow(color: Color.black.opacity(0.2), radius: 3, x: 0, y: 1)
                                .frame(width: 50, height: 50)
                            
                            Image(systemName: selectedItems.count == mediaItems.count ? "minus.circle" : "checkmark.circle")
                                .font(.system(size: 22))
                                .foregroundColor(.blue)
                        }
                    }
                    
                    // 删除按钮，仅在有选中项时显示
                    if !selectedItems.isEmpty {
                        Button(action: {
                            // 显示确认对话框
                            deleteSelectedItems()
                        }) {
                            ZStack {
                                Circle()
                                    .fill(Color(.systemBackground))
                                    .shadow(color: Color.black.opacity(0.2), radius: 3, x: 0, y: 1)
                                    .frame(width: 50, height: 50)
                                
                                Image(systemName: "trash")
                                    .font(.system(size: 20))
                                    .foregroundColor(.red)
                            }
                        }
                    }
                }
                .padding(20)
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottomTrailing)                
            }
        }
        .sheet(isPresented: $showFullScreen, onDismiss: {
            // 手势关闭时回调
            videoWrapper.cleanup()
        }) {
            if let item = selectedItem {
                // 添加全屏查看的下滑关闭功能
                FullScreenMediaView(item: item, 
                                isVideoLoading: $isVideoLoading, 
                                isImageLoading: $isImageLoading, 
                                showFullScreen: $showFullScreen,
                                onDelete: { item in
                                    performSingleDelete(item: item)
                                },
                                videoWrapper: videoWrapper)
            }
        }

    }
}

// 新增的全屏媒体查看视图组件，支持下滑关闭
struct FullScreenMediaView: View {
    let item: MediaType
    @Binding var isVideoLoading: Bool
    @Binding var isImageLoading: Bool
    @Binding var showFullScreen: Bool
    let onDelete: (MediaType) -> Void
    @ObservedObject var videoWrapper: VideoPlayerWrapper
    @State private var dragOffset: CGFloat = 0
    @State private var showCloseHint: Bool = false // 默认不显示，待检查用户设置
    @State private var showNoAudioWarning: Bool = false // 添加无声音警告状态
    
    var body: some View {
        ZStack {
            Color.black.edgesIgnoringSafeArea(.all)
                // 添加下滑关闭手势 - 应用在整个屏幕区域
                .contentShape(Rectangle())
                .gesture(
                    DragGesture()
                        .onChanged { gesture in
                            // 只响应向下滑动
                            if gesture.translation.height > 0 {
                                self.dragOffset = gesture.translation.height
                            }
                        }
                        .onEnded { gesture in
                            // 如果下滑超过50点，关闭全屏查看（降低阈值使关闭更容易）
                            if gesture.translation.height > 50 {
                                withAnimation {
                                    self.showFullScreen = false
                                }
                            } else {
                                // 否则回弹
                                withAnimation {
                                    self.dragOffset = 0
                                }
                            }
                        }
                )
            
            switch item {
            case .photo(let image):
                // 照片显示
                ZStack {
                    // 黑色背景
                    Color.black.ignoresSafeArea()
                    
                    if isImageLoading {
                        // 加载指示器
                        VStack {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(2.0)
                            Text("图片加载中...")
                                .foregroundColor(.white)
                                .padding(.top, 20)
                        }
                    } else {
                        // 照片内容
                        Image(uiImage: image)
                            .resizable()
                            .scaledToFit()
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .contentShape(Rectangle())
                            .onTapGesture {
                                // 点击照片时显示/隐藏控制层
                                withAnimation {
                                    showCloseHint.toggle()
                                }
                            }
                            // 确保在图片出现时将加载状态设为false
                            .onAppear {
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                    isImageLoading = false
                                }
                                
                                // 检查是否需要显示提示
                                checkAndShowHint()
                            }
                    }
                    
                    // 控制按钮层
                    VStack {
                        HStack {
                            // 左上角添加删除按钮
                            Button(action: {
                                onDelete(item)
                            }) {
                                Image(systemName: "trash.circle.fill")
                                    .font(.system(size: 30))
                                    .foregroundColor(.white)
                                    .padding()
                            }
                            
                            Spacer()
                            
                            // 右上角添加关闭按钮
                            Button(action: {
                                showFullScreen = false
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .font(.system(size: 44)) // 更大的尺寸
                                    .foregroundColor(.white)
                                    .padding()
                                    .shadow(color: .black, radius: 3, x: 0, y: 0)
                                    .background(Color.black.opacity(0.5))
                                    .clipShape(Circle())
                            }
                        }
                        .padding(.top, 8)
                        Spacer()
                    }
                }
                
            case .video(let url):
                if FileManager.default.fileExists(atPath: url.path) {
                    // 视频全屏显示
                    ZStack {
                        // 黑色背景层
                        Color.black.ignoresSafeArea()
                        
                        // 加载状态切换
                        if videoWrapper.isLoading {
                            // 加载指示器
                            VStack {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(2.0)
                                Text("视频加载中...")
                                    .foregroundColor(.white)
                                    .padding(.top, 20)
                            }
                        } else if let player = videoWrapper.player {
                            // 视频播放器
                            VideoPlayer(player: player)
                                .transition(.opacity)
                                .overlay(
                                    // 添加覆盖层以显示关闭按钮
                                    VStack {
                                        HStack {
                                            Spacer()
                                            Button(action: {
                                                showFullScreen = false
                                            }) {
                                                Image(systemName: "xmark.circle.fill")
                                                    .font(.system(size: 44)) // 更大尺寸
                                                    .foregroundColor(.white)
                                                    .padding()
                                                    .shadow(color: .black, radius: 3, x: 0, y: 0)
                                                    .background(Color.black.opacity(0.5))
                                                    .clipShape(Circle())
                                            }
                                        }
                                        .padding(.top, 10)
                                        Spacer()
                                    }
                                )
                                .onAppear {
                                    // 确保音频会话配置正确
                                    configureAudioSession()
                                    
                                    // 确保音量设置正确
                                    player.volume = 1.0
                                    
                                    // 强制重新播放以确保正确加载音频轨道
                                    let currentTime = player.currentTime()
                                    player.pause()
                                    player.seek(to: currentTime)
                                    player.play()
                                    
                                    print("[视频] 视频播放器已出现，音量已设置为: \(player.volume)")
                                }
                        }
                    }
                    .onAppear {
                        // 在视图出现时立即加载视频
                        print("[相册] 显示视频: \(url.lastPathComponent)")
                        
                        // 首先设置回调
                        videoWrapper.onLoadingChange = { isLoading in
                            DispatchQueue.main.async {
                                self.isVideoLoading = isLoading
                            }
                        }
                        
                        videoWrapper.onReadyToPlay = {
                            print("[相册] 视频就绪回调触发")
                        }
                        
                        // 音频状态变化回调
                        videoWrapper.onAudioStatusChange = { hasAudio in
                            DispatchQueue.main.async {
                                if !hasAudio {
                                    // 如果没有音频轨道，2秒后显示提示
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                                        withAnimation {
                                            self.showNoAudioWarning = true
                                        }
                                        
                                        // 5秒后自动隐藏提示
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                                            withAnimation {
                                                self.showNoAudioWarning = false
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 确保先设置状态再加载视频
                        isVideoLoading = true
                        
                        // 使用主线程优先级加载视频，确保UI响应性
                        DispatchQueue.main.async {
                            // 然后加载视频
                            _ = videoWrapper.loadVideo(url: url)
                            
                            // 检查视频是否有音频轨道
                            if !videoWrapper.hasAudioTrack {
                                // 如果没有音频轨道，显示警告
                                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                                    withAnimation {
                                        self.showNoAudioWarning = true
                                    }
                                    
                                    // 5秒后自动隐藏提示
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                                        withAnimation {
                                            self.showNoAudioWarning = false
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 检查是否需要显示提示
                        checkAndShowHint()
                    }
                    .onDisappear {
                        // 清理资源
                        videoWrapper.cleanup()
                    }
                } else {
                    Text("视频文件不存在")
                        .foregroundColor(.white)
                }
            }
            
            // 添加下滑提示
            if showCloseHint {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        VStack(spacing: 8) {
                            Image(systemName: "arrow.down")
                                .font(.system(size: 24, weight: .bold))
                                .foregroundColor(.white)
                            Text("下滑或点击关闭按钮退出")
                                .font(.system(size: 14))
                                .foregroundColor(.white)
                        }
                        .padding()
                        .background(Color.black.opacity(0.6))
                        .cornerRadius(12)
                        .padding(.bottom, 40) // 调整位置，不再需要避开已删除的底部按钮
                        Spacer()
                    }
                }
                .transition(.opacity)
                .animation(.easeInOut(duration: 0.5), value: showCloseHint)
            }
            
            // 添加无声音提示
            if showNoAudioWarning {
                VStack {
                    HStack {
                        Spacer()
                        VStack(spacing: 8) {
                            Image(systemName: "speaker.slash.fill")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(.white)
                            Text("此视频可能没有声音")
                                .font(.system(size: 14))
                                .foregroundColor(.white)
                        }
                        .padding()
                        .background(Color.black.opacity(0.7))
                        .cornerRadius(12)
                        .padding(.top, 40)
                        .padding(.trailing, 10)
                    }
                    Spacer()
                }
                .transition(.opacity)
                .animation(.easeInOut(duration: 0.5), value: showNoAudioWarning)
            }
        }
        // 应用拖动效果
        .offset(y: dragOffset / 3)
        .scaleEffect(1.0 - dragOffset / 2000)
        .animation(.interactiveSpring(), value: dragOffset)
    }
    
    // 新增：检查并显示下滑提示的方法
    private func checkAndShowHint() {
        // 通过用户偏好设置判断是否已经显示过提示
        let defaults = UserDefaults.standard
        let hintShownKey = "closeHintShown"
        
        if !defaults.bool(forKey: hintShownKey) {
            // 如果没有显示过，显示提示并标记为已显示
            withAnimation {
                showCloseHint = true
            }
            
            // 3秒后自动隐藏提示
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                withAnimation {
                    showCloseHint = false
                }
            }
            
            // 记录已经显示过提示
            defaults.set(true, forKey: hintShownKey)
        }
    }
    
    // 新增：配置音频会话
    private func configureAudioSession() {
        do {
            // 配置音频会话，确保声音可以通过扬声器播放
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback, mode: .default)
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
            print("[音频] 已配置音频会话为播放模式")
        } catch {
            print("[音频] 配置音频会话失败: \(error.localizedDescription)")
        }
    }
}
